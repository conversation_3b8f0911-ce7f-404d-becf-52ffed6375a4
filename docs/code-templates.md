# 代码模板集合

## 数据模型模板

### 基础响应模型模板

```dart
// lib/models/{module_name}_resp/{module_name}_resp.dart
import 'package:json_annotation/json_annotation.dart';

part '{module_name}_resp.g.dart';

@JsonSerializable()
class {ModuleName}Resp {
  String? id;
  String? name;
  String? description;
  int? priority;
  String? createdAt;
  String? updatedAt;

  {ModuleName}Resp({
    this.id,
    this.name,
    this.description,
    this.priority,
    this.createdAt,
    this.updatedAt,
  });

  factory {ModuleName}Resp.fromJson(Map<String, dynamic> json) => 
      _${ModuleName}RespFromJson(json);

  Map<String, dynamic> toJson() => _${ModuleName}RespToJson(this);
}
```

### 复杂响应模型模板（带关联）

```dart
// lib/models/{module_name}_resp/{module_name}_resp.dart
import 'package:json_annotation/json_annotation.dart';
import '../category_resp/category_resp.dart';

part '{module_name}_resp.g.dart';

@JsonSerializable()
class {ModuleName}Resp {
  String? id;
  String? name;
  String? description;
  int? priority;
  bool? isActive;
  List<String>? tags;
  List<CategoryResp>? categories;
  String? createdAt;
  String? updatedAt;

  {ModuleName}Resp({
    this.id,
    this.name,
    this.description,
    this.priority,
    this.isActive,
    this.tags,
    this.categories,
    this.createdAt,
    this.updatedAt,
  });

  factory {ModuleName}Resp.fromJson(Map<String, dynamic> json) => 
      _${ModuleName}RespFromJson(json);

  Map<String, dynamic> toJson() => _${ModuleName}RespToJson(this);
}
```

### 请求模型模板

```dart
// 在 add_widget 文件中定义
class {ModuleName}AddReq {
  String? id;
  String? name;
  String? description;
  int? priority;
  bool? isActive;
  List<String>? tags;
  List<String>? categoryIds;

  {ModuleName}AddReq({
    this.id,
    this.name,
    this.description,
    this.priority,
    this.isActive,
    this.tags,
    this.categoryIds,
  });

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'priority': priority,
      'isActive': isActive,
      'tags': tags,
      'categoryIds': categoryIds,
    };
  }
}
```

## API 接口模板

### 标准 CRUD 接口

```dart
// 在 lib/net/client.dart 中添加

// 列表查询
@GET('{module_name}s')
Future<ApiResult<PageListResp<List<{ModuleName}Resp>>>> {moduleName}List(
  @Query('currentpage') int currentpage,
  @Query('pagesize') int pagesize, {
  @Query('sortType') int sortType = 0,
});

// 添加
@POST('{module_name}')
Future<void> add{ModuleName}(@Body() Map<String, dynamic> data);

// 获取详情
@GET('{module_name}')
Future<ApiResult<{ModuleName}Resp>> get{ModuleName}Detail(@Query('id') String id);

// 删除
@DELETE('{module_name}')
Future<void> delete{ModuleName}(@Query('id') String id);

// 批量删除
@DELETE('{module_name}s')
Future<void> delete{ModuleName}Multi(@Query('ids') List<String> ids);

// 设置优先级
@POST('{module_name}/priority')
Future<void> set{ModuleName}Priority(@Body() Map<String, dynamic> data);
```

### 扩展接口模板

```dart
// 设置状态
@POST('{module_name}/status')
Future<void> set{ModuleName}Status(@Body() Map<String, dynamic> data);

// 批量操作
@POST('{module_name}s/batch')
Future<void> batch{ModuleName}Operation(@Body() Map<String, dynamic> data);

// 导出数据
@GET('{module_name}s/export')
Future<ApiResult<String>> export{ModuleName}s(
  @Query('format') String format,
  @Query('ids') List<String>? ids,
);

// 导入数据
@POST('{module_name}s/import')
@MultiPart()
Future<ApiResult<String>> import{ModuleName}s({
  @Part() required MultipartFile file,
});
```

## Controller 模板

### 基础 Controller

```dart
// lib/app/modules/{module_name}/controllers/{module_name}_controller.dart
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/{module_name}_resp/{module_name}_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';
import '../views/{module_name}_add_widget.dart';

class {ModuleName}Controller extends PageListController<{ModuleName}Resp> {
  
  @override
  Future<PageListResp<List<{ModuleName}Resp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().{moduleName}List(page, size, sortType: sortSelect.value);
    return result.data;
  }

  @override
  void updateSelect({ModuleName}Resp item, int index) async {
    await Get.dialog({ModuleName}AddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex({ModuleName}Resp item, int index) async {
    await Net.getRestClient().delete{ModuleName}(item.id ?? "");
    refreshDatasource();
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    await Net.getRestClient().add{ModuleName}(data);
    refreshDatasource();
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().delete{ModuleName}Multi(ids)
        .then((value) => refreshDatasource())
        .catchError((value) => value.toString().toast);
  }

  void setPriority({ModuleName}Resp item) {
    CommonInputDialog(
      title: "修改优先级(越大优先级越高)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.priority.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          await Net.getRestClient()
              .set{ModuleName}Priority({'id': item.id, 'priority': int.parse(content)})
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }
}
```

### 扩展 Controller（带筛选）

```dart
class {ModuleName}Controller extends PageListController<{ModuleName}Resp> {
  var categoryList = <CategoryResp>[];
  var categorySelect = CategoryResp().obs;
  var statusFilter = 0.obs; // 0: 全部, 1: 启用, 2: 禁用

  @override
  void onReady() {
    super.onReady();
    _loadCategories();
  }

  void _loadCategories() {
    Net.getRestClient().categoryList(1, 0).then((value) {
      categoryList.clear();
      categoryList.add(CategoryResp(id: "", name: "全部分类"));
      categoryList.addAll(value.data.data);
      categorySelect.value = categoryList.firstOrNull ?? CategoryResp();
      refreshDatasource();
    });
  }

  @override
  Future<PageListResp<List<{ModuleName}Resp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().{moduleName}List(
      page, 
      size, 
      categoryId: categorySelect.value.id ?? "",
      status: statusFilter.value,
      sortType: sortSelect.value,
    );
    return result.data;
  }

  void toggleStatus({ModuleName}Resp item) {
    Net.getRestClient()
        .set{ModuleName}Status({'id': item.id, 'isActive': !(item.isActive ?? false)})
        .then((value) => refreshDatasource())
        .catchError((value) => value.toString().toast);
  }

  // 其他方法保持不变...
}
```

## View 模板

### 基础 View

```dart
// lib/app/modules/{module_name}/views/{module_name}_view.dart
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/models/{module_name}_resp/{module_name}_resp.dart';

import '../controllers/{module_name}_controller.dart';
import '{module_name}_add_widget.dart';

class {ModuleName}View extends BasePageListView<{ModuleName}Resp, {ModuleName}Controller> {
  const {ModuleName}View({Key? key}) : super(key: key);

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID")),
      DataColumn2(label: Text("优先级"), fixedWidth: 100),
      DataColumn2(label: Text("名称")),
      DataColumn2(label: Text("描述")),
      DataColumn2(label: Text("状态"), fixedWidth: 100),
      DataColumn2(label: Text("操作"), fixedWidth: 120),
    ];
  }

  @override
  void onAddPressed() async {
    await Get.dialog(const {ModuleName}AddWidget(), barrierDismissible: false);
  }

  @override
  String getTitle() {
    return "{模块中文名}管理";
  }

  @override
  List<DataCell> buildDataCells({ModuleName}Resp item, int index) {
    return [
      DataCell(Text(item.id ?? "--")),
      DataCell(FilledButton(
        onPressed: () {
          controller.setPriority(item);
        },
        child: Text(item.priority?.toString() ?? "0"),
      )),
      DataCell(Text(item.name ?? "--")),
      DataCell(Text(item.description ?? "--")),
      DataCell(
        Switch(
          value: item.isActive ?? false,
          onChanged: (value) {
            controller.toggleStatus(item);
          },
        ),
      ),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(
                padding: const EdgeInsets.only(right: defaultPadding * 2),
                child: Text(LocaleKeys.operation_modify.tr)
              ),
              onPressed: () {
                controller.updateSelect(item, index);
              },
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.delete_outline),
              child: Padding(
                padding: const EdgeInsets.only(right: defaultPadding * 2),
                child: Text(LocaleKeys.operation_delete.tr)
              ),
              onPressed: () {
                controller.deleteByIndex(item, index);
              },
            )
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId({ModuleName}Resp item, int index) {
    return item.id;
  }
}
```

### 带筛选的 View

```dart
class {ModuleName}View extends BasePageListView<{ModuleName}Resp, {ModuleName}Controller> {
  const {ModuleName}View({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(getTitle()),
        centerTitle: false,
        actions: [
          // 分类筛选
          Obx(() => DropdownButton<CategoryResp>(
            value: controller.categorySelect.value,
            hint: const Text("选择分类"),
            onChanged: (CategoryResp? value) {
              if (value != null) {
                controller.categorySelect.value = value;
                controller.refreshDatasource();
              }
            },
            items: controller.categoryList.map((CategoryResp category) {
              return DropdownMenuItem<CategoryResp>(
                value: category,
                child: Text(category.name ?? ""),
              );
            }).toList(),
          )),
          const SizedBox(width: 16),
          // 状态筛选
          Obx(() => DropdownButton<int>(
            value: controller.statusFilter.value,
            onChanged: (int? value) {
              if (value != null) {
                controller.statusFilter.value = value;
                controller.refreshDatasource();
              }
            },
            items: const [
              DropdownMenuItem(value: 0, child: Text("全部状态")),
              DropdownMenuItem(value: 1, child: Text("启用")),
              DropdownMenuItem(value: 2, child: Text("禁用")),
            ],
          )),
          const SizedBox(width: 16),
          ...customActions(),
          // 其他标准操作按钮...
        ],
      ),
      body: super.build(context),
    );
  }

  // 其他方法保持不变...
}
```

## Binding 模板

```dart
// lib/app/modules/{module_name}/bindings/{module_name}_binding.dart
import 'package:get/get.dart';

import '../controllers/{module_name}_controller.dart';

class {ModuleName}Binding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<{ModuleName}Controller>(
      () => {ModuleName}Controller(),
    );
  }
}
```

## 添加表单模板

### 基础表单

```dart
// lib/app/modules/{module_name}/views/{module_name}_add_widget.dart
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/{module_name}_resp/{module_name}_resp.dart';
import '../controllers/{module_name}_controller.dart';

class {ModuleName}AddWidget extends StatefulWidget {
  final {ModuleName}Resp? resp;
  const {ModuleName}AddWidget({super.key, this.resp});

  @override
  State<{ModuleName}AddWidget> createState() => _{ModuleName}AddWidgetState();
}

class _{ModuleName}AddWidgetState extends State<{ModuleName}AddWidget> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  bool isActive = true;

  @override
  void initState() {
    super.initState();
    if (widget.resp != null) {
      nameController.text = widget.resp!.name ?? "";
      descriptionController.text = widget.resp!.description ?? "";
      isActive = widget.resp!.isActive ?? true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加{模块中文名}" : "编辑{模块中文名}"),
      content: SizedBox(
        width: 500,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: "名称 *",
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "名称不能为空";
                  }
                  return null;
                },
              ),
              const Gap(16),
              TextFormField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: "描述",
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const Gap(16),
              Row(
                children: [
                  const Text("状态："),
                  const Gap(8),
                  Switch(
                    value: isActive,
                    onChanged: (value) {
                      setState(() {
                        isActive = value;
                      });
                    },
                  ),
                  const Gap(8),
                  Text(isActive ? "启用" : "禁用"),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: _submit,
          child: const Text("确定"),
        ),
      ],
    );
  }

  void _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    {ModuleName}AddReq req = {ModuleName}AddReq(
      id: widget.resp?.id,
      name: nameController.text,
      description: descriptionController.text.isEmpty ? null : descriptionController.text,
      isActive: isActive,
    );

    try {
      await Get.find<{ModuleName}Controller>().addEntity(req.toMap());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    super.dispose();
  }
}
```

### 复杂表单（带文件上传和关联选择）

```dart
class {ModuleName}AddWidget extends StatefulWidget {
  final {ModuleName}Resp? resp;
  const {ModuleName}AddWidget({super.key, this.resp});

  @override
  State<{ModuleName}AddWidget> createState() => _{ModuleName}AddWidgetState();
}

class _{ModuleName}AddWidgetState extends State<{ModuleName}AddWidget> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  bool isActive = true;
  String? imageUrl;
  List<String> selectedCategoryIds = [];
  List<String> tags = [];
  final TextEditingController tagController = TextEditingController();

  List<CategoryResp> categoryList = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
    if (widget.resp != null) {
      _initializeData();
    }
  }

  void _loadCategories() async {
    try {
      var result = await Net.getRestClient().categoryList(1, 0);
      setState(() {
        categoryList = result.data.data;
      });
    } catch (e) {
      Get.snackbar("错误", "加载分类失败: $e");
    }
  }

  void _initializeData() {
    nameController.text = widget.resp!.name ?? "";
    descriptionController.text = widget.resp!.description ?? "";
    isActive = widget.resp!.isActive ?? true;
    imageUrl = widget.resp!.imageUrl;
    selectedCategoryIds = widget.resp!.categoryIds ?? [];
    tags = widget.resp!.tags ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加{模块中文名}" : "编辑{模块中文名}"),
      content: SizedBox(
        width: 600,
        height: 500,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 基础信息
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: "名称 *",
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "名称不能为空";
                    }
                    return null;
                  },
                ),
                const Gap(16),

                // 描述
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: "描述",
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const Gap(16),

                // 图片上传
                const Text("封面图片："),
                const Gap(8),
                _buildImageUpload(),
                const Gap(16),

                // 分类选择
                const Text("关联分类："),
                const Gap(8),
                _buildCategorySelection(),
                const Gap(16),

                // 标签管理
                const Text("标签："),
                const Gap(8),
                _buildTagManagement(),
                const Gap(16),

                // 状态开关
                Row(
                  children: [
                    const Text("状态："),
                    const Gap(8),
                    Switch(
                      value: isActive,
                      onChanged: (value) {
                        setState(() {
                          isActive = value;
                        });
                      },
                    ),
                    const Gap(8),
                    Text(isActive ? "启用" : "禁用"),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: isLoading ? null : _submit,
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text("确定"),
        ),
      ],
    );
  }

  Widget _buildImageUpload() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: imageUrl != null
          ? Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    imageUrl!,
                    width: double.infinity,
                    height: 120,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: IconButton(
                    onPressed: () {
                      setState(() {
                        imageUrl = null;
                      });
                    },
                    icon: const Icon(Icons.close, color: Colors.red),
                  ),
                ),
              ],
            )
          : InkWell(
              onTap: _pickImage,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.add_photo_alternate, size: 40),
                  Text("点击上传图片"),
                ],
              ),
            ),
    );
  }

  Widget _buildCategorySelection() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: categoryList.map((category) {
        final isSelected = selectedCategoryIds.contains(category.id);
        return FilterChip(
          label: Text(category.name ?? ""),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                selectedCategoryIds.add(category.id!);
              } else {
                selectedCategoryIds.remove(category.id);
              }
            });
          },
        );
      }).toList(),
    );
  }

  Widget _buildTagManagement() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: tagController,
                decoration: const InputDecoration(
                  hintText: "输入标签",
                  border: OutlineInputBorder(),
                ),
                onSubmitted: _addTag,
              ),
            ),
            const Gap(8),
            IconButton(
              onPressed: () => _addTag(tagController.text),
              icon: const Icon(Icons.add),
            ),
          ],
        ),
        const Gap(8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: tags.map((tag) {
            return Chip(
              label: Text(tag),
              deleteIcon: const Icon(Icons.close, size: 18),
              onDeleted: () {
                setState(() {
                  tags.remove(tag);
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !tags.contains(tag)) {
      setState(() {
        tags.add(tag);
        tagController.clear();
      });
    }
  }

  void _pickImage() async {
    // 实现图片选择和上传逻辑
    // 参考现有的图片上传实现
  }

  void _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      {ModuleName}AddReq req = {ModuleName}AddReq(
        id: widget.resp?.id,
        name: nameController.text,
        description: descriptionController.text.isEmpty ? null : descriptionController.text,
        isActive: isActive,
        imageUrl: imageUrl,
        categoryIds: selectedCategoryIds,
        tags: tags,
      );

      await Get.find<{ModuleName}Controller>().addEntity(req.toMap());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    tagController.dispose();
    super.dispose();
  }
}
```

## PanelConfig 集成模板

```dart
// 在 lib/app/modules/home/<USER>/home_controller.dart 的 menuConfigs 中添加

MenuItemConfig(
  label: "{模块中文名}管理",
  icon: const Icon(Icons.{icon_name}_outlined),
  selectedIcon: const Icon(Icons.{icon_name}),
  viewBuilder: () => const {ModuleName}View(),
  controllerInitializer: () => Get.put({ModuleName}Controller(), permanent: true),
  category: "{category}", // "user", "source", "vip" 等
),
```

## 常用图标参考

```dart
// 管理类
Icons.admin_panel_settings // 管理面板
Icons.settings_applications // 应用设置
Icons.manage_accounts // 账户管理

// 内容类
Icons.article // 文章
Icons.library_books // 图书
Icons.video_library // 视频库
Icons.image // 图片
Icons.audio_file // 音频

// 分类类
Icons.category // 分类
Icons.label // 标签
Icons.folder // 文件夹
Icons.class_ // 类别

// 用户类
Icons.person // 用户
Icons.group // 群组
Icons.account_circle // 账户
Icons.verified_user // 认证用户

// 系统类
Icons.settings // 设置
Icons.security // 安全
Icons.storage // 存储
Icons.analytics // 分析
```

这些模板提供了完整的模块开发所需的代码结构，可以根据具体需求进行调整和扩展。
```
