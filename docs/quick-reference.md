# 快速参考指南

## 文件清单

创建新模块时需要创建的文件：

### 必需文件
```
lib/models/{module_name}_resp/
├── {module_name}_resp.dart          # 数据模型
└── {module_name}_resp.g.dart        # 自动生成的序列化代码

lib/app/modules/{module_name}/
├── bindings/
│   └── {module_name}_binding.dart   # 依赖注入绑定
├── controllers/
│   └── {module_name}_controller.dart # 控制器
└── views/
    ├── {module_name}_view.dart      # 主视图
    └── {module_name}_add_widget.dart # 添加/编辑表单
```

### 需要修改的文件
```
lib/net/client.dart                  # 添加 API 接口
lib/app/modules/home/<USER>/home_controller.dart # 添加菜单配置
```

## 命名规范速查

| 类型 | 规范 | 示例 |
|------|------|------|
| 模块名 | 下划线分隔小写 | `user_role`, `system_config` |
| 文件名 | 模块名+功能后缀 | `user_role_controller.dart` |
| 类名 | 驼峰命名法 | `UserRoleController` |
| API 端点 | RESTful 风格 | `/api/v1/admin/user_roles` |
| 变量名 | 驼峰命名法 | `userRoleList` |

## API 接口速查

### 标准 CRUD 接口模式
```dart
// 列表查询
@GET('{module_name}s')
Future<ApiResult<PageListResp<List<{ModuleName}Resp>>>> {moduleName}List(
  @Query('currentpage') int currentpage,
  @Query('pagesize') int pagesize, {
  @Query('sortType') int sortType = 0,
});

// 添加/更新
@POST('{module_name}')
Future<void> add{ModuleName}(@Body() Map<String, dynamic> data);

// 获取详情
@GET('{module_name}')
Future<ApiResult<{ModuleName}Resp>> get{ModuleName}Detail(@Query('id') String id);

// 删除
@DELETE('{module_name}')
Future<void> delete{ModuleName}(@Query('id') String id);

// 批量删除
@DELETE('{module_name}s')
Future<void> delete{ModuleName}Multi(@Query('ids') List<String> ids);

// 设置优先级
@POST('{module_name}/priority')
Future<void> set{ModuleName}Priority(@Body() Map<String, dynamic> data);
```

## Controller 必需方法

继承 `PageListController<T>` 时必须实现：

```dart
// 获取分页数据
@override
Future<PageListResp<List<T>>> getPageList(int startIndex, int size);

// 编辑操作
@override
void updateSelect(T item, int index);

// 单个删除
@override
void deleteByIndex(T item, int index);

// 添加实体
@override
Future<void> addEntity(Map<String, dynamic> data);

// 批量删除
@override
void deleteMulti(List<String> ids);
```

## View 必需方法

继承 `BasePageListView<Model, Controller>` 时必须实现：

```dart
// 定义表格列
@override
List<DataColumn> getDataColumns();

// 添加按钮处理
@override
void onAddPressed();

// 页面标题
@override
String getTitle();

// 构建数据行
@override
List<DataCell> buildDataCells(Model item, int index);

// 获取模型ID
@override
String? getModelId(Model item, int index);
```

## 数据模型注解

```dart
import 'package:json_annotation/json_annotation.dart';

part '{module_name}_resp.g.dart';

@JsonSerializable()
class {ModuleName}Resp {
  // 字段定义...
  
  factory {ModuleName}Resp.fromJson(Map<String, dynamic> json) => 
      _${ModuleName}RespFromJson(json);

  Map<String, dynamic> toJson() => _${ModuleName}RespToJson(this);
}
```

## 菜单配置模板

```dart
// 在 HomeController.menuConfigs 中添加
MenuItemConfig(
  label: "{模块中文名}管理",
  icon: const Icon(Icons.{icon_name}_outlined),
  selectedIcon: const Icon(Icons.{icon_name}),
  viewBuilder: () => const {ModuleName}View(),
  controllerInitializer: () => Get.put({ModuleName}Controller(), permanent: true),
  category: "{category}",
),
```

## 常用分类

| 分类 | 说明 | 示例模块 |
|------|------|----------|
| "home" | 首页相关 | 仪表板 |
| "user" | 用户管理 | 用户、角色、权限 |
| "source" | 资源管理 | 分类、资源、系列 |
| "vip" | 会员管理 | 兑换码、订阅 |
| "system" | 系统管理 | 配置、日志、任务 |

## 常用图标

| 功能类型 | 推荐图标 |
|----------|----------|
| 用户管理 | `Icons.person`, `Icons.group`, `Icons.account_circle` |
| 角色权限 | `Icons.admin_panel_settings`, `Icons.verified_user` |
| 内容管理 | `Icons.article`, `Icons.library_books`, `Icons.video_library` |
| 分类标签 | `Icons.category`, `Icons.label`, `Icons.folder` |
| 系统设置 | `Icons.settings`, `Icons.tune`, `Icons.build` |
| 数据分析 | `Icons.analytics`, `Icons.bar_chart`, `Icons.pie_chart` |

## 表单验证模式

```dart
TextFormField(
  controller: controller,
  decoration: const InputDecoration(
    labelText: "字段名 *",
    border: OutlineInputBorder(),
  ),
  validator: (value) {
    if (value == null || value.isEmpty) {
      return "字段名不能为空";
    }
    // 其他验证逻辑
    return null;
  },
),
```

## 错误处理模式

```dart
try {
  await Net.getRestClient().someMethod();
  Get.snackbar("成功", "操作成功");
} catch (e) {
  Get.snackbar("错误", e.toString());
}
```

## 状态管理模式

```dart
// 在 Controller 中
var isLoading = false.obs;
var selectedItem = Rxn<ItemType>();

// 在 View 中
Obx(() => isLoading.value 
  ? CircularProgressIndicator()
  : YourWidget()
)
```

## 生成命令

```bash
# 生成序列化代码
flutter packages pub run build_runner build

# 清理并重新生成
flutter packages pub run build_runner build --delete-conflicting-outputs

# 监听模式（开发时使用）
flutter packages pub run build_runner watch
```

## 调试技巧

### 1. 网络请求调试
- 查看控制台的网络日志
- 使用 PrettyDioLogger 查看详细请求信息

### 2. 状态管理调试
- 使用 `print()` 或 `debugPrint()` 输出状态变化
- 检查 `Obx()` 是否正确包装响应式变量

### 3. 路由导航调试
- 确认 Controller 是否正确注册
- 检查 `viewBuilder` 返回的组件类型

## 性能优化要点

### 1. 避免不必要的重建
- 合理使用 `Obx()` 包装需要响应的部分
- 避免在 `build` 方法中创建新对象

### 2. 网络请求优化
- 实现适当的缓存机制
- 避免重复请求相同数据

### 3. 内存管理
- 在 `dispose()` 中释放资源
- 使用 `Get.lazyPut()` 进行懒加载

## 常见错误及解决方案

### 1. 序列化错误
```
错误：Missing "part" directive
解决：确保添加 part '{module_name}_resp.g.dart';
```

### 2. 控制器未找到
```
错误：Controller not found
解决：检查 Binding 中是否正确注册控制器
```

### 3. API 调用失败
```
错误：HTTP 404/500
解决：检查 API 路径和参数是否正确
```

### 4. 页面导航异常
```
错误：Widget 无法显示
解决：检查 viewBuilder 和 controllerInitializer
```

## 测试检查清单

- [ ] 列表查询功能正常
- [ ] 添加功能正常
- [ ] 编辑功能正常
- [ ] 删除功能正常
- [ ] 批量删除功能正常
- [ ] 排序功能正常
- [ ] 分页功能正常
- [ ] 表单验证正常
- [ ] 错误处理正常
- [ ] 菜单导航正常

这个快速参考指南提供了开发过程中最常用的信息，可以帮助快速查找和解决问题。
