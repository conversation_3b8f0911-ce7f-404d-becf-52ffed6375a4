# 添加新模块指南

## 优化后的配置方式

现在添加新模块变得非常简单！您只需要在一个地方进行配置。

## 步骤

### 1. 创建模块文件
按照现有模块的结构创建新模块：
- `lib/app/modules/your_module/controllers/your_module_controller.dart`
- `lib/app/modules/your_module/views/your_module_view.dart`
- `lib/app/modules/your_module/bindings/your_module_binding.dart`

### 2. 在统一配置中添加模块
只需要在 `lib/app/modules/home/<USER>/home_controller.dart` 文件中的 `menuConfigs` 列表中添加一个新的 `MenuItemConfig`：

```dart
// 在 menuConfigs 列表中添加
MenuItemConfig(
  label: "您的模块名称",
  icon: const Icon(Icons.your_icon_outlined),
  selectedIcon: const Icon(Icons.your_icon),
  viewBuilder: () => const YourModuleView(),
  controllerInitializer: () => Get.put(YourModuleController(), permanent: true),
  category: "适当的分类", // 可选值: "user", "source", "vip", 或新的分类
),
```

### 3. 如果是新分类
如果您的模块属于新的分类，需要在 `side_menu.dart` 中添加相应的分组显示：

```dart
// 在 side_menu.dart 中添加新分类的获取方法
List<DrawerItem> get yourCategoryDestinations => HomeController.menuConfigs
    .where((config) => config.category == "your_category")
    .map((config) => DrawerItem(config.label, config.icon, config.selectedIcon))
    .toList();

// 在 build 方法中添加显示逻辑
Padding(
  padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
  child: Text(
    "您的分类名称",
    style: Theme.of(context).textTheme.titleSmall,
  ),
),
...yourCategoryDestinations.map(
  (DrawerItem destination) {
    return NavigationDrawerDestination(
      label: Text(destination.label),
      icon: destination.icon,
      selectedIcon: destination.selectedIcon,
    );
  },
),
```

## 优势

1. **单一配置点**: 只需要在 `menuConfigs` 中添加配置
2. **自动同步**: DrawerItem 和 PanelConfig 自动从统一配置生成
3. **类型安全**: 编译时检查，减少运行时错误
4. **易于维护**: 所有菜单配置集中管理
5. **减少重复**: 不需要在多个地方重复相同的信息

## 注意事项

- 确保新模块的 import 语句已添加到 `home_controller.dart`
- 菜单项的索引会自动对应，无需手动管理
- 如果需要国际化，可以在 label 中使用 `LocaleKeys.xxx.tr`
