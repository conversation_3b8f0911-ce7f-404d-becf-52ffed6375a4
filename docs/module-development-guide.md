# 模块开发标准化指南

## 概述

本文档基于现有的 `resource` 和 `resource_type`（category_type）模块的代码分析，提供了一个完整的模块开发标准化指南。该指南旨在确保新模块与现有架构保持一致性，提高开发效率和代码质量。

## 架构概览

### 前后端分离设计模式

系统采用前后端分离架构：
- **前端**: Flutter Web 应用，使用 GetX 状态管理
- **后端**: RESTful API 服务
- **通信**: 通过 HTTP 请求进行数据交互

### 核心设计模式

1. **MVC 模式**: Model-View-Controller 分离
2. **Repository 模式**: 数据访问层抽象
3. **依赖注入**: 使用 GetX 进行依赖管理
4. **响应式编程**: 使用 Rx 进行状态管理

## 文件组织结构

### 标准目录结构

```
lib/
├── app/modules/{module_name}/
│   ├── bindings/{module_name}_binding.dart
│   ├── controllers/{module_name}_controller.dart
│   └── views/
│       ├── {module_name}_view.dart
│       └── {module_name}_add_widget.dart
├── models/{module_name}_resp/
│   ├── {module_name}_resp.dart
│   └── {module_name}_resp.g.dart
└── net/
    └── client.dart (API 接口定义)
```

### 命名规范

- **模块名**: 使用下划线分隔的小写字母 (如: `resource_type`)
- **文件名**: 模块名 + 功能后缀 (如: `resource_controller.dart`)
- **类名**: 驼峰命名法 (如: `ResourceController`)
- **API 端点**: RESTful 风格 (如: `/api/v1/admin/resources`)

## 数据模型定义标准

### 响应模型 (Resp)

```dart
import 'package:json_annotation/json_annotation.dart';

part '{module_name}_resp.g.dart';

@JsonSerializable()
class {ModuleName}Resp {
  String? id;
  String? name;
  String? description;
  int? priority;
  
  {ModuleName}Resp({
    this.id,
    this.name,
    this.description,
    this.priority,
  });

  factory {ModuleName}Resp.fromJson(Map<String, dynamic> json) => 
      _${ModuleName}RespFromJson(json);

  Map<String, dynamic> toJson() => _${ModuleName}RespToJson(this);
}
```

### 请求模型 (Req)

```dart
class {ModuleName}AddReq {
  String? name;
  String? description;
  int? priority;

  {ModuleName}AddReq({
    this.name,
    this.description,
    this.priority,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'priority': priority,
    };
  }
}
```

## API 接口设计规范

### RestClient 接口定义

```dart
// 在 lib/net/client.dart 中添加

// 列表查询 - 支持分页和排序
@GET('{module_name}s')
Future<ApiResult<PageListResp<List<{ModuleName}Resp>>>> {moduleName}List(
  @Query('currentpage') int currentpage,
  @Query('pagesize') int pagesize, {
  @Query('sortType') int sortType = 0,
});

// 单个添加
@POST('{module_name}')
Future<void> add{ModuleName}(@Body() Map<String, dynamic> data);

// 获取详情
@GET('{module_name}')
Future<ApiResult<{ModuleName}Resp>> get{ModuleName}Detail(@Query('id') String id);

// 单个删除
@DELETE('{module_name}')
Future<void> delete{ModuleName}(@Query('id') String id);

// 批量删除
@DELETE('{module_name}s')
Future<void> delete{ModuleName}Multi(@Query('ids') List<String> ids);

// 设置优先级
@POST('{module_name}/priority')
Future<void> set{ModuleName}Priority(@Body() Map<String, dynamic> data);
```

### HTTP 方法使用规范

- **GET**: 查询数据（列表、详情）
- **POST**: 创建数据、复杂操作
- **DELETE**: 删除数据
- **PUT**: 完整更新（较少使用）
- **PATCH**: 部分更新（较少使用）

### 返回值类型设计

- **列表查询**: `ApiResult<PageListResp<List<T>>>`
- **详情查询**: `ApiResult<T>`
- **操作结果**: `Future<void>` 或 `ApiResult<String>`

## Controller 实现模式

### 基础 Controller 模板

```dart
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/{module_name}_resp/{module_name}_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';
import '../views/{module_name}_add_widget.dart';

class {ModuleName}Controller extends PageListController<{ModuleName}Resp> {
  
  @override
  Future<PageListResp<List<{ModuleName}Resp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().{moduleName}List(page, size, sortType: sortSelect.value);
    return result.data;
  }

  @override
  void updateSelect({ModuleName}Resp item, int index) async {
    await Get.dialog({ModuleName}AddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex({ModuleName}Resp item, int index) async {
    await Net.getRestClient().delete{ModuleName}(item.id ?? "");
    refreshDatasource();
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    await Net.getRestClient().add{ModuleName}(data);
    refreshDatasource();
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().delete{ModuleName}Multi(ids)
        .then((value) => refreshDatasource())
        .catchError((value) => value.toString().toast);
  }

  // 设置优先级的通用方法
  void setPriority({ModuleName}Resp item) {
    CommonInputDialog(
      title: "修改优先级(越大优先级越高)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.priority.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          await Net.getRestClient()
              .set{ModuleName}Priority({'id': item.id, 'priority': int.parse(content)})
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }
}
```

### PageListController 核心方法

必须实现的抽象方法：
- `getPageList()`: 获取分页数据
- `updateSelect()`: 编辑操作
- `deleteByIndex()`: 单个删除
- `addEntity()`: 添加实体
- `deleteMulti()`: 批量删除

## View 实现模式

### 基础 View 模板

```dart
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/models/{module_name}_resp/{module_name}_resp.dart';

import '../controllers/{module_name}_controller.dart';
import '{module_name}_add_widget.dart';

class {ModuleName}View extends BasePageListView<{ModuleName}Resp, {ModuleName}Controller> {
  const {ModuleName}View({Key? key}) : super(key: key);

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID")),
      DataColumn2(label: Text("优先级"), fixedWidth: 100),
      DataColumn2(label: Text("名称")),
      DataColumn2(label: Text("描述")),
      DataColumn2(label: Text("操作"), fixedWidth: 100),
    ];
  }

  @override
  void onAddPressed() async {
    await Get.dialog(const {ModuleName}AddWidget(), barrierDismissible: false);
  }

  @override
  String getTitle() {
    return "{模块中文名}管理";
  }

  @override
  List<DataCell> buildDataCells({ModuleName}Resp item, int index) {
    return [
      DataCell(Text(item.id.toString())),
      DataCell(FilledButton(
        onPressed: () {
          controller.setPriority(item);
        },
        child: Text(item.priority.toString()),
      )),
      DataCell(Text(item.name ?? "--")),
      DataCell(Text(item.description ?? "--")),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(
                padding: const EdgeInsets.only(right: defaultPadding * 2),
                child: Text(LocaleKeys.operation_modify.tr)
              ),
              onPressed: () {
                controller.updateSelect(item, index);
              },
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.delete_outline),
              child: Padding(
                padding: const EdgeInsets.only(right: defaultPadding * 2),
                child: Text(LocaleKeys.operation_delete.tr)
              ),
              onPressed: () {
                controller.deleteByIndex(item, index);
              },
            )
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId({ModuleName}Resp item, int index) {
    return item.id;
  }
}
```

### BasePageListView 核心方法

必须实现的抽象方法：
- `getDataColumns()`: 定义表格列
- `onAddPressed()`: 添加按钮处理
- `getTitle()`: 页面标题
- `buildDataCells()`: 构建数据行
- `getModelId()`: 获取模型ID

## Binding 实现模式

### 标准 Binding 模板

```dart
import 'package:get/get.dart';

import '../controllers/{module_name}_controller.dart';

class {ModuleName}Binding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<{ModuleName}Controller>(
      () => {ModuleName}Controller(),
    );
  }
}
```

## PanelConfig 集成配置

### 统一配置方式

系统采用统一的菜单配置方式，只需要在一个地方添加新模块配置：

#### 1. 在 HomeController 中添加模块配置

```dart
// 在 lib/app/modules/home/<USER>/home_controller.dart 的 menuConfigs 中添加
MenuItemConfig(
  label: "{模块中文名}管理",
  icon: const Icon(Icons.{icon_name}_outlined),
  selectedIcon: const Icon(Icons.{icon_name}),
  viewBuilder: () => const {ModuleName}View(),
  controllerInitializer: () => Get.put({ModuleName}Controller(), permanent: true),
  category: "source", // 或其他适当的分类
),
```

#### 2. 分类说明

- **"home"**: 首页相关
- **"user"**: 用户管理相关
- **"source"**: 资源管理相关（如分类、资源等）
- **"vip"**: 会员管理相关

#### 3. 如果需要新分类

在 `lib/app/modules/home/<USER>/side_menu.dart` 中添加：

```dart
// 添加新分类的获取方法
List<DrawerItem> get {category}Destinations => HomeController.menuConfigs
    .where((config) => config.category == "{category}")
    .map((config) => DrawerItem(config.label, config.icon, config.selectedIcon))
    .toList();

// 在 build 方法中添加显示逻辑
Padding(
  padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
  child: Text(
    "{分类中文名}",
    style: Theme.of(context).textTheme.titleSmall,
  ),
),
...{category}Destinations.map(
  (DrawerItem destination) {
    return NavigationDrawerDestination(
      label: Text(destination.label),
      icon: destination.icon,
      selectedIcon: destination.selectedIcon,
    );
  },
),
```

### 配置优势

1. **单一配置点**: 只需要在 `menuConfigs` 中添加配置
2. **自动同步**: DrawerItem 和 PanelConfig 自动从统一配置生成
3. **类型安全**: 编译时检查，减少运行时错误
4. **易于维护**: 所有菜单配置集中管理

## 添加表单组件模式

### 基础添加表单模板

```dart
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/{module_name}_resp/{module_name}_resp.dart';
import '../controllers/{module_name}_controller.dart';

class {ModuleName}AddWidget extends StatefulWidget {
  final {ModuleName}Resp? resp;
  const {ModuleName}AddWidget({super.key, this.resp});

  @override
  State<{ModuleName}AddWidget> createState() => _{ModuleName}AddWidgetState();
}

class _{ModuleName}AddWidgetState extends State<{ModuleName}AddWidget> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.resp != null) {
      nameController.text = widget.resp!.name ?? "";
      descriptionController.text = widget.resp!.description ?? "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加{模块中文名}" : "编辑{模块中文名}"),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: "名称",
                border: OutlineInputBorder(),
              ),
            ),
            const Gap(16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: "描述",
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (nameController.text.isEmpty) {
              Get.snackbar("错误", "名称不能为空");
              return;
            }

            Map<String, dynamic> data = {
              'name': nameController.text,
              'description': descriptionController.text,
            };

            if (widget.resp != null) {
              data['id'] = widget.resp!.id;
            }

            try {
              await Get.find<{ModuleName}Controller>().addEntity(data);
              Get.back();
              Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
            } catch (e) {
              Get.snackbar("错误", e.toString());
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    super.dispose();
  }
}
```

## Step-by-Step 开发流程

### 第一步：创建数据模型

1. 在 `lib/models/` 下创建 `{module_name}_resp` 目录
2. 创建 `{module_name}_resp.dart` 文件
3. 定义响应模型类，使用 `@JsonSerializable()` 注解
4. 运行 `flutter packages pub run build_runner build` 生成序列化代码

### 第二步：添加 API 接口

1. 在 `lib/net/client.dart` 中添加相关 API 接口
2. 遵循 RESTful 设计规范
3. 使用适当的 HTTP 方法和返回类型
4. 运行 `flutter packages pub run build_runner build` 生成客户端代码

### 第三步：创建 Controller

1. 在 `lib/app/modules/{module_name}/controllers/` 下创建控制器
2. 继承 `PageListController<T>`
3. 实现所有抽象方法
4. 添加特定业务逻辑方法

### 第四步：创建 View

1. 在 `lib/app/modules/{module_name}/views/` 下创建视图
2. 继承 `BasePageListView<Model, Controller>`
3. 实现所有抽象方法
4. 定义表格列和数据行

### 第五步：创建 Binding

1. 在 `lib/app/modules/{module_name}/bindings/` 下创建绑定
2. 继承 `Bindings`
3. 在 `dependencies()` 方法中注册控制器

### 第六步：创建添加表单

1. 在 `views` 目录下创建 `{module_name}_add_widget.dart`
2. 实现表单验证和提交逻辑
3. 支持添加和编辑两种模式

### 第七步：集成到系统

1. 在 `HomeController.menuConfigs` 中添加模块配置
2. 确保导入语句正确
3. 选择合适的分类和图标

### 第八步：测试验证

1. 测试列表查询功能
2. 测试添加、编辑、删除功能
3. 测试分页和排序功能
4. 测试批量删除功能

## 常见问题和解决方案

### 1. 序列化代码生成失败

**问题**: 运行 build_runner 时出错
**解决方案**:
- 检查 `part` 语句是否正确
- 确保所有依赖包版本兼容
- 清理缓存：`flutter clean && flutter pub get`

### 2. API 接口调用失败

**问题**: 网络请求返回错误
**解决方案**:
- 检查 API 路径是否正确
- 验证参数类型和名称
- 查看网络拦截器日志

### 3. 页面导航异常

**问题**: 点击菜单项无响应
**解决方案**:
- 确保 Controller 已正确注册
- 检查 `viewBuilder` 返回的组件
- 验证 `controllerInitializer` 逻辑

### 4. 数据刷新问题

**问题**: 操作后数据未更新
**解决方案**:
- 在操作成功后调用 `refreshDatasource()`
- 检查 `getPageList()` 方法实现
- 验证 API 返回数据格式

## 最佳实践

### 1. 代码规范

- 使用一致的命名规范
- 添加适当的注释和文档
- 遵循 Dart 代码风格指南

### 2. 错误处理

- 使用 try-catch 捕获异常
- 提供用户友好的错误提示
- 记录详细的错误日志

### 3. 性能优化

- 合理使用 `Obx()` 进行局部刷新
- 避免不必要的 Widget 重建
- 优化网络请求频率

### 4. 用户体验

- 提供加载状态指示
- 实现合理的表单验证
- 支持键盘快捷操作

## 总结

本指南提供了一个完整的模块开发框架，基于现有的成熟模块设计模式。通过遵循这些标准和最佳实践，可以确保新模块与现有系统的一致性，提高开发效率和代码质量。

关键要点：
1. 遵循统一的文件组织结构
2. 使用标准的 API 设计模式
3. 继承基础控制器和视图类
4. 采用统一的配置集成方式
5. 实施完整的测试验证流程

通过这个指南，AI 助手和开发者都能够快速、准确地创建符合系统架构的新功能模块。
