# AI 助手模块开发提示词模板

## 基础提示词

```
请基于现有的 resource 和 category_type 模块的设计模式，创建一个新的 {模块名} 模块。

要求：
1. 遵循现有的架构设计模式
2. 使用统一的文件组织结构
3. 实现完整的 CRUD 功能
4. 集成到 PanelConfig 系统中

模块需求：
- 模块名称：{模块名}
- 中文名称：{中文名称}
- 主要字段：{字段列表}
- 特殊功能：{特殊功能描述}

请按照以下步骤实现：
1. 创建数据模型
2. 添加 API 接口
3. 实现 Controller
4. 创建 View 组件
5. 添加 Binding
6. 创建表单组件
7. 集成到系统菜单
8. 提供测试建议
```

## 具体模块示例提示词

### 示例1：用户角色管理模块

```
请创建一个用户角色管理模块，参考 category_type 模块的实现模式。

模块规格：
- 模块名：user_role
- 中文名：用户角色
- 主要字段：
  - id: 角色ID
  - name: 角色名称
  - description: 角色描述
  - permissions: 权限列表
  - priority: 优先级
  - isActive: 是否启用

特殊功能：
- 支持权限分配
- 支持角色启用/禁用
- 支持优先级排序

请确保：
1. 继承 PageListController 基类
2. 使用 BasePageListView 视图基类
3. 实现标准的 CRUD 操作
4. 添加到 "user" 分类下
5. 使用 Icons.admin_panel_settings 图标
```

### 示例2：系统配置管理模块

```
请创建一个系统配置管理模块，参考 resource 模块的复杂实现模式。

模块规格：
- 模块名：system_config
- 中文名：系统配置
- 主要字段：
  - id: 配置ID
  - key: 配置键
  - value: 配置值
  - type: 配置类型（string/number/boolean/json）
  - category: 配置分类
  - description: 配置描述
  - isEditable: 是否可编辑

特殊功能：
- 支持不同数据类型的配置值
- 支持配置分类筛选
- 支持配置导入导出
- 支持配置历史记录

请确保：
1. 实现复杂的表单验证
2. 支持不同类型的输入组件
3. 添加配置分类筛选功能
4. 实现配置备份和恢复
5. 添加到新的 "system" 分类下
```

## 快速开发提示词

### 最小化实现

```
请快速创建一个 {模块名} 模块的最小化实现，包含基础的 CRUD 功能。

基础字段：
- id, name, description, priority

请生成：
1. 数据模型文件
2. Controller 文件
3. View 文件
4. Binding 文件
5. 添加表单文件
6. PanelConfig 集成代码

使用标准模板，无需特殊功能。
```

### 复制现有模块

```
请完全复制 {源模块名} 模块的实现，创建一个新的 {目标模块名} 模块。

修改内容：
- 将所有 {源模块名} 替换为 {目标模块名}
- 将中文名称改为 {新中文名称}
- 添加以下新字段：{新字段列表}
- 修改图标为：{新图标}
- 分类改为：{新分类}

请确保所有文件名、类名、变量名都正确更新。
```

## 问题排查提示词

### 编译错误排查

```
我的 {模块名} 模块出现编译错误，请帮助排查和修复。

错误信息：
{错误信息}

相关文件：
{文件列表}

请检查：
1. 导入语句是否正确
2. 类名和文件名是否匹配
3. 抽象方法是否都已实现
4. 序列化代码是否已生成
5. API 接口定义是否正确
```

### 功能异常排查

```
我的 {模块名} 模块功能异常，请帮助排查。

异常现象：
{异常描述}

已检查项目：
{已检查的内容}

请帮助检查：
1. Controller 中的方法实现
2. API 接口调用
3. 数据模型序列化
4. 页面导航配置
5. 错误处理逻辑
```

## 优化改进提示词

### 性能优化

```
请优化我的 {模块名} 模块的性能，参考最佳实践。

当前问题：
{性能问题描述}

请检查和优化：
1. 不必要的 Widget 重建
2. 网络请求优化
3. 内存泄漏问题
4. 状态管理优化
5. 列表渲染性能
```

### 用户体验改进

```
请改进我的 {模块名} 模块的用户体验。

改进方向：
1. 添加加载状态指示
2. 改进表单验证提示
3. 优化错误处理
4. 添加操作确认对话框
5. 改进响应式布局

请提供具体的代码改进建议。
```

## 测试相关提示词

### 功能测试

```
请为 {模块名} 模块创建完整的功能测试用例。

测试范围：
1. 列表查询功能
2. 添加功能
3. 编辑功能
4. 删除功能
5. 批量操作
6. 排序和筛选
7. 表单验证
8. 错误处理

请提供详细的测试步骤和预期结果。
```

### 集成测试

```
请验证 {模块名} 模块与系统的集成是否正确。

检查项目：
1. 菜单导航是否正常
2. 权限控制是否生效
3. 数据持久化是否正确
4. API 接口是否正常
5. 错误处理是否完整
6. 用户体验是否一致

请提供集成测试的检查清单。
```

## 使用说明

### 如何使用这些提示词

1. **选择合适的模板**：根据需求复杂度选择对应的提示词模板
2. **填充具体信息**：将模板中的占位符替换为实际的模块信息
3. **逐步实现**：按照提示词的步骤逐一实现功能
4. **测试验证**：使用测试相关的提示词进行功能验证
5. **优化改进**：使用优化提示词持续改进模块质量

### 提示词定制

可以根据具体项目需求，对提示词模板进行定制：

- 添加项目特定的约束条件
- 修改代码风格要求
- 调整功能实现优先级
- 增加特定的测试要求

### 最佳实践

1. **明确需求**：在使用提示词前，明确模块的具体需求和功能范围
2. **参考现有模块**：始终以现有的成功模块为参考基准
3. **逐步验证**：每完成一个步骤都要进行验证，确保正确性
4. **文档更新**：及时更新相关文档，保持一致性

这些提示词模板可以帮助 AI 助手更准确地理解需求，并生成符合项目架构的高质量代码。
