import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/theme/constants.dart';

class ThemeController extends GetxController {
  var themeMode = ThemeMode.light.obs;
  var colorSelected = ColorSeed.baseColor.obs;
  var imageSelected = ColorImageProvider.leaves.obs;
  var imageColorScheme = const ColorScheme.light().obs;
  var colorSelectionMethod = ColorSelectionMethod.colorSeed.obs;

  void handleBrightnessChange() {
    themeMode.value = Get.isDarkMode ? ThemeMode.light : ThemeMode.dark;
  }

  void handleColorSelect(int value) {
    colorSelectionMethod.value = ColorSelectionMethod.colorSeed;
    colorSelected.value = ColorSeed.values[value];
  }

  void handleImageSelect(int value) {
    colorSelectionMethod.value = ColorSelectionMethod.image;
    final String url = ColorImageProvider.values[value].url;
    ColorScheme.fromImageProvider(provider: NetworkImage(url))
        .then((newScheme) {
      imageSelected.value = ColorImageProvider.values[value];
      imageColorScheme.value = newScheme;
    });
  }
}
