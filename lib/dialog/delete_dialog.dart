import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class DeleteDialog extends StatefulWidget {
  final VoidCallback onPressed;
  final String? title;
  const DeleteDialog({super.key, this.title, required this.onPressed});

  @override
  State<DeleteDialog> createState() => _DeleteDialogState();
}

class _DeleteDialogState extends State<DeleteDialog> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Card(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  textAlign: TextAlign.center,
                  widget.title ?? "是否确定删除?",
                  style: Get.textTheme.titleLarge,
                ),
                const Gap(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FilledButton(
                      onPressed: () {
                        widget.onPressed();
                        Get.back();
                      },
                      child: const Text("确定"),
                    ),
                    const Gap(10),
                    FilledButton(
                      onPressed: () {
                        Get.back();
                      },
                      child: const Text("取消"),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
