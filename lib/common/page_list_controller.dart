import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/dialog/delete_dialog.dart';
import 'package:lsenglish_admin/dialog/dialog_extension.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';

import 'page_view.dart';

abstract class PageListController<T> extends GetxController {
  PageDataSourceAsync? pageDataSourceAsync;
  var showDeleteButton = false.obs;
  var sortSelect = 0.obs;
  Future<PageListResp<List<T>>> getPageList(int startIndex, int size);

  void onTableSelect(ValueKey rowKey, bool selected) {
    pageDataSourceAsync?.setRowSelection(rowKey, selected);
    showDeleteButton.value = pageDataSourceAsync?.selectedRowCount != 0;
  }

  void refreshDatasource() {
    pageDataSourceAsync?.refreshDatasource();
  }

  @override
  void onClose() {
    pageDataSourceAsync?.dispose();
    super.onClose();
  }

  String? getDeleteDialogTitle() {
    return null;
  }

  Future<void> addEntity(Map<String, dynamic> data);
  void updateSelect(T item, int index);
  void deleteByIndex(T item, int index);

  void deleteSelect() {
    DeleteDialog(
      title: getDeleteDialogTitle(),
      onPressed: () {
        List<String>? stringList = pageDataSourceAsync?.selectionRowKeys
            .map((key) => key as ValueKey<String?>)
            .map((valueKey) => valueKey.value)
            .whereType<String>()
            .toList();
        if (stringList != null) {
          deleteMulti(stringList);
          showDeleteButton.value = false;
        }
      },
    ).dialog();
  }

  void deleteMulti(List<String> ids);
}
