import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';

import '../util/priority.dart';
import 'page_list_controller.dart';

abstract class BasePageListView<Model, T extends PageListController> extends GetView<T> {
  const BasePageListView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PageDataSourceAsync pageDataSourceAsync = PageDataSourceAsync<Model>(controller, buildDataRow);
    controller.pageDataSourceAsync = pageDataSourceAsync;
    return Scaffold(
      appBar: AppBar(
        title: Text(getTitle()),
        centerTitle: false,
        actions: [
          ...customActions(),
          Obx(() => DropdownButton<int>(
                value: controller.sortSelect.value,
                icon: const Icon(Icons.arrow_downward),
                elevation: 16,
                focusColor: Colors.transparent,
                onChanged: (int? value) {
                  controller.sortSelect.value = value ?? 0;
                  controller.refreshDatasource();
                },
                items: sortDropdownItems,
              )),
          const Gap(20),
          Obx(() => Visibility(
                visible: controller.showDeleteButton.value,
                child: FilledButton(
                  onPressed: () {
                    controller.deleteSelect();
                  },
                  child: const Text("删除"),
                ),
              )),
          IconButton(onPressed: onAddPressed, icon: const Icon(Icons.add_circle_outline_rounded))
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(defaultPadding),
          child: AsyncPaginatedDataTable2(
            headingRowColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
              return Get.theme.primaryColor.withOpacity(0.08);
            }),
            renderEmptyRowsInTheEnd: false,
            headingCheckboxTheme: CheckboxThemeData(side: BorderSide(color: Get.theme.primaryColor, width: 2.0)),
            isHorizontalScrollBarVisible: true,
            isVerticalScrollBarVisible: true,
            columnSpacing: 16,
            columns: getDataColumns(),
            source: pageDataSourceAsync,
          ),
        ),
      ),
    );
  }

  DataRow2 buildDataRow(Model item, int index) {
    return DataRow2(
        specificRowHeight: getSpecificRowHeight(),
        onDoubleTap: () {
          controller.updateSelect(item, index);
        },
        key: ValueKey<String?>(getModelId(item, index)),
        onSelectChanged: (value) {
          if (value != null) {
            controller.onTableSelect(ValueKey<String?>(getModelId(item, index)), value);
          }
        },
        cells: buildDataCells(item, index));
  }

  void onAddPressed();
  List<DataColumn> getDataColumns();
  String getTitle();
  double? getSpecificRowHeight() {
    return 50;
  }

  String? getModelId(Model item, int index);
  List<DataCell> buildDataCells(Model item, int index);
  List<Widget> customActions() {
    return [];
  }
}

class PageDataSourceAsync<T> extends AsyncDataTableSource {
  PageListController controller;
  final DataRow Function(T item, int index) buildDataRow;
  PageDataSourceAsync(this.controller, this.buildDataRow);

  @override
  Future<AsyncRowsResponse> getRows(int startIndex, int count) async {
    var x = await controller.getPageList(startIndex, count);
    var r = AsyncRowsResponse(x.total ?? 0, List<DataRow>.generate(x.data.length, (index) => buildDataRow(x.data[index], index)));
    return r;
  }
}
