import 'package:dio/dio.dart';

import 'exception.dart';

class ApiInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.statusCode == 200) {
      // 如果响应的状态码是200，首先检查响应体中的状态
      final responseBody = response.data;
      if (responseBody is Map) {
        final status = responseBody['status'];
        final message = responseBody['message'];
        if (status != null && status != 200) {
          // 如果内部状态不是200，使用handler.reject来触发错误处理
          handler.reject(
            DioException(
              requestOptions: response.requestOptions,
              response: response,
              error: ApiException(message, status),
            ),
            true,
          );
        } else {
          // 如果内部状态是200，正常处理响应
          handler.next(response);
        }
      } else {
        // 如果响应体不是Map类型，可能是因为返回的不是JSON格式
        handler.next(response);
      }
    } else {
      // 如果响应的状态码不是200，直接通过handler.reject触发错误处理
      handler.reject(
        DioException(
          requestOptions: response.requestOptions,
          response: response,
          error:
              ApiException('Invalid response status code', response.statusCode),
        ),
        true,
      );
    }
  }
}
