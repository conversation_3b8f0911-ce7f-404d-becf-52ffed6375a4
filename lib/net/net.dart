import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../net/client.dart';
import '../net/interceptor.dart';

class Net {
  static RestClient? _restClient;
  static final Dio _dio = Dio();
  static final Dio _dioDownload = Dio();

  static void configureDio({required String baseUrl}) {
    _dio.options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 100),
      receiveTimeout: const Duration(seconds: 100),
      contentType: "application/json",
      responseType: ResponseType.json,
    );
    _dioDownload.options = BaseOptions(
      connectTimeout: const Duration(seconds: 100),
      receiveTimeout: const Duration(seconds: 100),
    );
    //根据业务端传入的type来设置网络请求日志级别
    if (kDebugMode) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
      ));
    }
    // _dio.httpClientAdapter = BrowserHttpClientAdapter(withCredentials: true);
    _dio.interceptors.add(ApiInterceptor());
  }

  static RestClient getRestClient() {
    _restClient ??= RestClient(_dio);
    return _restClient!;
  }

  static Dio getDio() {
    return _dio;
  }

  static Dio getDownloadDio() {
    return _dioDownload;
  }
}
