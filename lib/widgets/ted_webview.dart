import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/widgets/dialog_wrap.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

typedef TedWebviewCallback = void Function(String downloadUrl);

class TedWebviewWidget extends StatefulWidget {
  final String tedUrl;
  final TedWebviewCallback tedWebviewCallback;
  const TedWebviewWidget({super.key, required this.tedUrl, required this.tedWebviewCallback});

  @override
  State<TedWebviewWidget> createState() => _TedWebviewWidgetState();
}

class _TedWebviewWidgetState extends State<TedWebviewWidget> {
  late final WebViewController webviewController;
  var webViewLoadingProgress = 0.0;
  @override
  void initState() {
    super.initState();
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller = WebViewController.fromPlatformCreationParams(params);

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {
            debugPrint('Page started loading: $url');
          },
          onPageFinished: (String url) {
            debugPrint('Page finished loading: $url');
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('''
                Page resource error:
                code: ${error.errorCode}
                description: ${error.description}
                errorType: ${error.errorType}
                isForMainFrame: ${error.isForMainFrame}
          ''');
          },
          onNavigationRequest: (NavigationRequest request) async {
            debugPrint("request.url=${request.url}");
            // request.url=https://download.ted.com/products/178566.mp4?apikey=acme-roadrunner
            if (request.url.contains('products/downloads')) {
              widget.tedWebviewCallback(request.url);
              Get.back();
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onUrlChange: (UrlChange change) {
            debugPrint('url change to ${change.url}');
          },
          onHttpAuthRequest: (HttpAuthRequest request) {},
        ),
      )
      ..loadRequest(Uri.parse(widget.tedUrl));

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController).setMediaPlaybackRequiresUserGesture(false);
    }

    webviewController = controller;
  }

  @override
  void dispose() {
    webviewController.clearCache();
    webviewController.clearLocalStorage();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogWrapWidget(maxWidth: Get.width * 0.7, child: WebViewWidget(controller: webviewController));
  }
}
