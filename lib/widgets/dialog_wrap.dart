import 'package:flutter/material.dart';

class DialogWrapWidget extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  const DialogWrapWidget({super.key, required this.child, required this.maxWidth});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: maxWidth),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}
