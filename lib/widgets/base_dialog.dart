import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/widgets/dialog_wrap.dart';

class CommonDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<String> options;
  final List<VoidCallback> callbacks;
  const CommonDialog({
    super.key,
    required this.title,
    this.subtitle = "",
    required this.options,
    required this.callbacks,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: Get.width * 0.3,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Gap(8),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            Visibility(
              visible: subtitle != "",
              child: Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  subtitle,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            ...List.generate(options.length, (index) {
              return Column(
                children: [
                  const Divider(),
                  const Gap(8),
                  GestureDetector(
                    onTap: () {
                      callbacks[index]();
                      Get.back();
                    },
                    child: Text(
                      options[index],
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Get.theme.primaryColor,
                      ),
                    ),
                  ),
                ],
              );
            }),
            const Gap(8),
            const Divider(),
            GestureDetector(
              onTap: () => Get.back(),
              child: Text(
                "取消",
                style: TextStyle(fontSize: 18, color: Get.theme.colorScheme.error),
              ),
            ),
            const Gap(16),
          ],
        ),
      ),
    );
  }
}

typedef InputCallback = void Function(String content);

class CommonInputDialog extends StatefulWidget {
  final String title;
  final String inputContent;
  final InputCallback sureCallback;
  final List<TextInputFormatter>? inputFormatters;
  const CommonInputDialog({
    super.key,
    required this.title,
    this.inputContent = "",
    this.inputFormatters,
    required this.sureCallback,
  });

  @override
  State<CommonInputDialog> createState() => _CommonInputDialogState();
}

class _CommonInputDialogState extends State<CommonInputDialog> {
  TextEditingController textEditingController = TextEditingController();
  @override
  void initState() {
    super.initState();
    textEditingController.text = widget.inputContent;
  }

  @override
  Widget build(BuildContext context) {
    return DialogWrapWidget(
      maxWidth: Get.width * 0.2,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Gap(8),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              widget.title,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          TextField(
            inputFormatters: widget.inputFormatters,
            controller: textEditingController,
          ),
          const Gap(16),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FilledButton(
                onPressed: () {
                  widget.sureCallback(textEditingController.text);
                  Get.back();
                },
                child: const Text("确定"),
              ),
              const Gap(10),
              FilledButton(
                onPressed: () {
                  Get.back();
                },
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                  foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                ),
                child: const Text("取消"),
              )
            ],
          ),
          const Gap(16),
        ],
      ),
    );
  }
}
