import 'package:json_annotation/json_annotation.dart';

part 'page_list_resp.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class PageListResp<T> {
  const PageListResp({
    required this.total,
    required this.data,
  });

  factory PageListResp.fromJson(
          Map<String, dynamic> json, T Function(Object?) fromJsonT) =>
      _$PageListRespFromJson(json, fromJsonT);

  final int? total;
  final T data;

  Map<String, dynamic> toJson(Object? Function(T) toJsonT) =>
      _$PageListRespToJson(this, toJsonT);
}
