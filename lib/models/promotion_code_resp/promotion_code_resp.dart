import 'package:json_annotation/json_annotation.dart';

part 'promotion_code_resp.g.dart';

@JsonSerializable()
class PromotionCodeResp {
  int? id;
  DateTime? createat;
  DateTime? updateat;
  int? status;
  String? code;
  int? days;
  String? uid;
  int? redeemedTimestamp;

  PromotionCodeResp({
    this.id,
    this.createat,
    this.updateat,
    this.status,
    this.code,
    this.days,
    this.uid,
    this.redeemedTimestamp,
  });

  factory PromotionCodeResp.fromJson(Map<String, dynamic> json) {
    return _$PromotionCodeRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PromotionCodeRespToJson(this);
}
