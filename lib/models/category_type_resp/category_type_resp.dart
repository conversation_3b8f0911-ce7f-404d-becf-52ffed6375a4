import 'package:json_annotation/json_annotation.dart';

part 'category_type_resp.g.dart';

@JsonSerializable()
class CategoryTypeResp {
  String? id;
  String? name;
  String? description;
  int? priority;

  CategoryTypeResp({this.id, this.name, this.priority, this.description});

  factory CategoryTypeResp.fromJson(Map<String, dynamic> json) {
    return _$CategoryTypeRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$CategoryTypeRespToJson(this);
}
