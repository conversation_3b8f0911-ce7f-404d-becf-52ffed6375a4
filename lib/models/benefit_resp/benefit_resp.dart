import 'package:json_annotation/json_annotation.dart';

part 'benefit_resp.g.dart';

@JsonSerializable()
class BenefitResp {
  int? id;
  String? name;
  String? code;
  int? level;
  int? cycleType; // 1-日，2-周，3-月，4-季，5-年，6-无周期
  int? cycleCount;
  int? benefitCount;
  int? sort;
  int? status; // 0-禁用，1-启用
  int? benefitGroupId;
  String? benefitGroupName;
  String? benefitGroupCode;
  String? description;
  String? createdAt;
  String? updatedAt;

  BenefitResp({
    this.id,
    this.name,
    this.code,
    this.level,
    this.cycleType,
    this.cycleCount,
    this.benefitCount,
    this.sort,
    this.status,
    this.benefitGroupId,
    this.benefitGroupName,
    this.benefitGroupCode,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory BenefitResp.fromJson(Map<String, dynamic> json) =>
      _$BenefitRespFromJson(json);

  Map<String, dynamic> toJson() => _$BenefitRespToJson(this);

  // 获取周期类型的显示文本
  String get cycleTypeText {
    switch (cycleType) {
      case 1:
        return '日';
      case 2:
        return '周';
      case 3:
        return '月';
      case 4:
        return '季';
      case 5:
        return '年';
      case 6:
        return '无周期';
      default:
        return '未知';
    }
  }

  // 获取状态的显示文本
  String get statusText {
    switch (status) {
      case 0:
        return '禁用';
      case 1:
        return '启用';
      default:
        return '未知';
    }
  }
}

@JsonSerializable()
class BenefitGroupResp {
  int? id;
  String? name;
  String? code;
  int? status; // 0-下线，1-上线
  String? description;
  String? createdAt;
  String? updatedAt;

  BenefitGroupResp({
    this.id,
    this.name,
    this.code,
    this.status,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory BenefitGroupResp.fromJson(Map<String, dynamic> json) =>
      _$BenefitGroupRespFromJson(json);

  Map<String, dynamic> toJson() => _$BenefitGroupRespToJson(this);

  // 获取状态的显示文本
  String get statusText {
    switch (status) {
      case 0:
        return '下线';
      case 1:
        return '上线';
      default:
        return '未知';
    }
  }
}

@JsonSerializable()
class UserBenefitResp {
  int? id;
  String? uid;
  String? benefitGroupCode;
  int? benefitId;
  String? benefitName;
  String? benefitCode;
  int? benefitGroupId;
  int? remain;
  int? total;
  String? lastRefreshTime;
  int? lastRefreshTimestamp;
  String? nextRefreshTime;
  int? nextRefreshTimestamp;
  int? status; // 0-失效，1-启用
  String? source;
  String? createdAt;
  String? updatedAt;

  UserBenefitResp({
    this.id,
    this.uid,
    this.benefitGroupCode,
    this.benefitId,
    this.benefitName,
    this.benefitCode,
    this.benefitGroupId,
    this.remain,
    this.total,
    this.lastRefreshTime,
    this.lastRefreshTimestamp,
    this.nextRefreshTime,
    this.nextRefreshTimestamp,
    this.status,
    this.source,
    this.createdAt,
    this.updatedAt,
  });

  factory UserBenefitResp.fromJson(Map<String, dynamic> json) =>
      _$UserBenefitRespFromJson(json);

  Map<String, dynamic> toJson() => _$UserBenefitRespToJson(this);

  // 获取状态的显示文本
  String get statusText {
    switch (status) {
      case 0:
        return '失效';
      case 1:
        return '启用';
      default:
        return '未知';
    }
  }

  // 获取使用进度百分比
  double get usageProgress {
    if (total == null || total == 0) return 0.0;
    if (remain == null) return 0.0;
    int used = total! - remain!;
    return used / total!;
  }

  // 获取使用进度文本
  String get usageProgressText {
    if (total == null || remain == null) return '--/--';
    int used = total! - remain!;
    return '$used/$total';
  }
}
