// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'benefit_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BenefitResp _$BenefitRespFromJson(Map<String, dynamic> json) => BenefitResp(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      code: json['code'] as String?,
      level: (json['level'] as num?)?.toInt(),
      cycleType: (json['cycleType'] as num?)?.toInt(),
      cycleCount: (json['cycleCount'] as num?)?.toInt(),
      benefitCount: (json['benefitCount'] as num?)?.toInt(),
      sort: (json['sort'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      benefitGroupId: (json['benefitGroupId'] as num?)?.toInt(),
      benefitGroupName: json['benefitGroupName'] as String?,
      benefitGroupCode: json['benefitGroupCode'] as String?,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );

Map<String, dynamic> _$BenefitRespToJson(BenefitResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'level': instance.level,
      'cycleType': instance.cycleType,
      'cycleCount': instance.cycleCount,
      'benefitCount': instance.benefitCount,
      'sort': instance.sort,
      'status': instance.status,
      'benefitGroupId': instance.benefitGroupId,
      'benefitGroupName': instance.benefitGroupName,
      'benefitGroupCode': instance.benefitGroupCode,
      'description': instance.description,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

BenefitGroupResp _$BenefitGroupRespFromJson(Map<String, dynamic> json) =>
    BenefitGroupResp(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      code: json['code'] as String?,
      status: (json['status'] as num?)?.toInt(),
      description: json['description'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );

Map<String, dynamic> _$BenefitGroupRespToJson(BenefitGroupResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'status': instance.status,
      'description': instance.description,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

UserBenefitResp _$UserBenefitRespFromJson(Map<String, dynamic> json) =>
    UserBenefitResp(
      id: (json['id'] as num?)?.toInt(),
      uid: json['uid'] as String?,
      benefitGroupCode: json['benefitGroupCode'] as String?,
      benefitId: (json['benefitId'] as num?)?.toInt(),
      benefitName: json['benefitName'] as String?,
      benefitCode: json['benefitCode'] as String?,
      benefitGroupId: (json['benefitGroupId'] as num?)?.toInt(),
      remain: (json['remain'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
      lastRefreshTime: json['lastRefreshTime'] as String?,
      lastRefreshTimestamp: (json['lastRefreshTimestamp'] as num?)?.toInt(),
      nextRefreshTime: json['nextRefreshTime'] as String?,
      nextRefreshTimestamp: (json['nextRefreshTimestamp'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      source: json['source'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );

Map<String, dynamic> _$UserBenefitRespToJson(UserBenefitResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'benefitGroupCode': instance.benefitGroupCode,
      'benefitId': instance.benefitId,
      'benefitName': instance.benefitName,
      'benefitCode': instance.benefitCode,
      'benefitGroupId': instance.benefitGroupId,
      'remain': instance.remain,
      'total': instance.total,
      'lastRefreshTime': instance.lastRefreshTime,
      'lastRefreshTimestamp': instance.lastRefreshTimestamp,
      'nextRefreshTime': instance.nextRefreshTime,
      'nextRefreshTimestamp': instance.nextRefreshTimestamp,
      'status': instance.status,
      'source': instance.source,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
