import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish_admin/models/series_resp/series_resp.dart';

import '../category_resp/category_resp.dart';
import 'resource_relation_resp.dart';

part 'resource_resp.g.dart';

@JsonSerializable()
class ResourceResp {
  String? id;
  String? defaultLangTitle;
  //这些列表只有获取详情的时候才有
  List<ResourceRelationResp>? resourceRelations;
  ResourceRelationResp? originResourceRelation;
  List<CategoryResp>? categories;
  List<SeriesResp>? serieses;
  String? videoUrl;
  String? cover;
  int? duration;
  String? type;
  String? publishedAt;
  String? author;
  bool? isFeaturedContent;
  int? priority;
  List<String>? tags;

  ResourceResp({
    this.id,
    this.resourceRelations,
    this.originResourceRelation,
    this.categories,
    this.videoUrl,
    this.cover,
    this.duration,
    this.type,
    this.publishedAt,
    this.author,
    this.defaultLangTitle,
    this.serieses,
    this.isFeaturedContent,
    this.priority,
    this.tags,
  });

  factory ResourceResp.fromJson(Map<String, dynamic> json) {
    return _$ResourceRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceRespToJson(this);
}
