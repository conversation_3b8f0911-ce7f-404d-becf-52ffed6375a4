// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resource_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResourceResp _$ResourceRespFromJson(Map<String, dynamic> json) => ResourceResp(
      id: json['id'] as String?,
      resourceRelations: (json['resourceRelations'] as List<dynamic>?)
          ?.map((e) => ResourceRelationResp.fromJson(e as Map<String, dynamic>))
          .toList(),
      originResourceRelation: json['originResourceRelation'] == null
          ? null
          : ResourceRelationResp.fromJson(
              json['originResourceRelation'] as Map<String, dynamic>),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => CategoryResp.fromJson(e as Map<String, dynamic>))
          .toList(),
      videoUrl: json['videoUrl'] as String?,
      cover: json['cover'] as String?,
      duration: (json['duration'] as num?)?.toInt(),
      type: json['type'] as String?,
      publishedAt: json['publishedAt'] as String?,
      author: json['author'] as String?,
      defaultLangTitle: json['defaultLangTitle'] as String?,
      serieses: (json['serieses'] as List<dynamic>?)
          ?.map((e) => SeriesResp.fromJson(e as Map<String, dynamic>))
          .toList(),
      isFeaturedContent: json['isFeaturedContent'] as bool?,
      priority: (json['priority'] as num?)?.toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ResourceRespToJson(ResourceResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'defaultLangTitle': instance.defaultLangTitle,
      'resourceRelations': instance.resourceRelations,
      'originResourceRelation': instance.originResourceRelation,
      'categories': instance.categories,
      'serieses': instance.serieses,
      'videoUrl': instance.videoUrl,
      'cover': instance.cover,
      'duration': instance.duration,
      'type': instance.type,
      'publishedAt': instance.publishedAt,
      'author': instance.author,
      'isFeaturedContent': instance.isFeaturedContent,
      'priority': instance.priority,
      'tags': instance.tags,
    };
