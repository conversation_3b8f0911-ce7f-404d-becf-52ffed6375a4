import 'package:json_annotation/json_annotation.dart';

part 'resource_relation_resp.g.dart';

@JsonSerializable()
class ResourceRelationResp {
  String? langCode;
  String? title;
  String? description;
  String? subtitleUrl;

  ResourceRelationResp({this.langCode, this.title, this.description, this.subtitleUrl});

  factory ResourceRelationResp.fromJson(Map<String, dynamic> json) => _$ResourceRelationRespFromJson(json);

  Map<String, dynamic> toJson() => _$ResourceRelationRespToJson(this);
}
