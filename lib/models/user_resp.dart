import 'package:json_annotation/json_annotation.dart';

part 'user_resp.g.dart';

@JsonSerializable()
class UserResp {
  String? id;
  String? username;
  String? nickname;
  int? status;
  String? avatar;
  String? roleName;

  UserResp({this.id, this.username, this.nickname, this.status, this.avatar,this.roleName});

  factory UserResp.fromJson(Map<String, dynamic> json) =>
      _$UserRespFromJson(json);

  Map<String, dynamic> toJson() => _$UserRespToJson(this);
}
