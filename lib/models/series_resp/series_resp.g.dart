// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'series_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SeriesRelation _$SeriesRelationFromJson(Map<String, dynamic> json) =>
    SeriesRelation(
      langCode: json['langCode'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      statement: json['statement'] as String?,
    );

Map<String, dynamic> _$SeriesRelationToJson(SeriesRelation instance) =>
    <String, dynamic>{
      'langCode': instance.langCode,
      'title': instance.title,
      'description': instance.description,
      'statement': instance.statement,
    };

SeriesResp _$SeriesRespFromJson(Map<String, dynamic> json) => SeriesResp(
      id: json['id'] as String?,
      cover: json['cover'] as String?,
      priority: (json['priority'] as num?)?.toInt(),
      isFeaturedContent: json['isFeaturedContent'] as bool?,
      categoryIds: (json['categoryIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      seriesRelations: (json['seriesRelations'] as List<dynamic>?)
          ?.map((e) => SeriesRelation.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SeriesRespToJson(SeriesResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'priority': instance.priority,
      'cover': instance.cover,
      'seriesRelations': instance.seriesRelations,
      'categoryIds': instance.categoryIds,
      'isFeaturedContent': instance.isFeaturedContent,
    };
