import 'package:json_annotation/json_annotation.dart';

part 'series_resp.g.dart';

@JsonSerializable()
class SeriesRelation {
  String? langCode;
  String? title;
  String? description;
  String? statement;

  SeriesRelation({this.langCode, this.title, this.description, this.statement});

  factory SeriesRelation.fromJson(Map<String, dynamic> json) => _$SeriesRelationFromJson(json);
  Map<String, dynamic> toJson() => _$SeriesRelationToJson(this);
}

@JsonSerializable()
class SeriesResp {
  String? id;
  int? priority;
  String? cover;
  List<SeriesRelation>? seriesRelations;
  List<String>? categoryIds;
  bool? isFeaturedContent;

  SeriesResp({this.id, this.cover, this.priority, this.isFeaturedContent, this.categoryIds, this.seriesRelations});

  factory SeriesResp.fromJson(Map<String, dynamic> json) {
    return _$SeriesRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SeriesRespToJson(this);
}
