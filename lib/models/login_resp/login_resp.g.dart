// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginResp _$<PERSON>gin<PERSON>p<PERSON>rom<PERSON>(Map<String, dynamic> json) => LoginResp(
      user: json['user'] == null
          ? null
          : UserResp.fromJson(json['user'] as Map<String, dynamic>),
      token: json['token'] as String?,
    );

Map<String, dynamic> _$LoginRespToJson(LoginResp instance) => <String, dynamic>{
      'user': instance.user,
      'token': instance.token,
    };
