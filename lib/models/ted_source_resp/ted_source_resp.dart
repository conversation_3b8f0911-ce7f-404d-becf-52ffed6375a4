import 'package:json_annotation/json_annotation.dart';

import 'language.dart';
import 'subtitle.dart';

part 'ted_source_resp.g.dart';

@JsonSerializable()
class TedSourceResp {
  String? id;
  String? shortenedUrl;
  String? playerUrl;
  String? description;
  String? presenterDisplayName;
  String? title;
  String? socialTitle;
  String? publishedAt;
  int? duration;
  int? viewedCount;
  String? canonical;
  String? thumb;
  String? slug;
  String? m3u8;
  String? metadata;
  List<TedSourceSubtitle>? subtitles;
  List<TedSourceLanguage>? languages;
  String? youTubeUrl;

  TedSourceResp({
    this.id,
    this.shortenedUrl,
    this.playerUrl,
    this.description,
    this.presenterDisplayName,
    this.title,
    this.socialTitle,
    this.publishedAt,
    this.duration,
    this.viewedCount,
    this.canonical,
    this.thumb,
    this.slug,
    this.m3u8,
    this.metadata,
    this.subtitles,
    this.languages,
    this.youTubeUrl,
  });

  factory TedSourceResp.fromJson(Map<String, dynamic> json) {
    return _$TedSourceRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TedSourceRespToJson(this);
}
