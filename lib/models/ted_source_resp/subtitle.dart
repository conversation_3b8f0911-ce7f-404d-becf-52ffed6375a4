import 'package:json_annotation/json_annotation.dart';

part 'subtitle.g.dart';

@JsonSerializable()
class TedSourceSubtitle {
  String? code;
  String? name;
  String? webvtt;

  TedSourceSubtitle({this.code, this.name, this.webvtt});

  factory TedSourceSubtitle.fromJson(Map<String, dynamic> json) {
    return _$TedSourceSubtitleFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TedSourceSubtitleToJson(this);
}
