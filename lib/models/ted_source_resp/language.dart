import 'package:json_annotation/json_annotation.dart';

part 'language.g.dart';

@JsonSerializable()
class TedSourceLanguage {
  String? languageName;
  String? endonym;
  String? languageCode;
  String? ianaCode;

  TedSourceLanguage({
    this.languageName,
    this.endonym,
    this.languageCode,
    this.ianaCode,
  });

  factory TedSourceLanguage.fromJson(Map<String, dynamic> json) {
    return _$TedSourceLanguageFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TedSourceLanguageToJson(this);
}
