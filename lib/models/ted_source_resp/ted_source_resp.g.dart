// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ted_source_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TedSourceResp _$TedSourceRespFromJson(Map<String, dynamic> json) =>
    TedSourceResp(
      id: json['id'] as String?,
      shortenedUrl: json['shortenedUrl'] as String?,
      playerUrl: json['playerUrl'] as String?,
      description: json['description'] as String?,
      presenterDisplayName: json['presenterDisplayName'] as String?,
      title: json['title'] as String?,
      socialTitle: json['socialTitle'] as String?,
      publishedAt: json['publishedAt'] as String?,
      duration: (json['duration'] as num?)?.toInt(),
      viewedCount: (json['viewedCount'] as num?)?.toInt(),
      canonical: json['canonical'] as String?,
      thumb: json['thumb'] as String?,
      slug: json['slug'] as String?,
      m3u8: json['m3u8'] as String?,
      metadata: json['metadata'] as String?,
      subtitles: (json['subtitles'] as List<dynamic>?)
          ?.map((e) => TedSourceSubtitle.fromJson(e as Map<String, dynamic>))
          .toList(),
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => TedSourceLanguage.fromJson(e as Map<String, dynamic>))
          .toList(),
      youTubeUrl: json['youTubeUrl'] as String?,
    );

Map<String, dynamic> _$TedSourceRespToJson(TedSourceResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shortenedUrl': instance.shortenedUrl,
      'playerUrl': instance.playerUrl,
      'description': instance.description,
      'presenterDisplayName': instance.presenterDisplayName,
      'title': instance.title,
      'socialTitle': instance.socialTitle,
      'publishedAt': instance.publishedAt,
      'duration': instance.duration,
      'viewedCount': instance.viewedCount,
      'canonical': instance.canonical,
      'thumb': instance.thumb,
      'slug': instance.slug,
      'm3u8': instance.m3u8,
      'metadata': instance.metadata,
      'subtitles': instance.subtitles,
      'languages': instance.languages,
      'youTubeUrl': instance.youTubeUrl,
    };
