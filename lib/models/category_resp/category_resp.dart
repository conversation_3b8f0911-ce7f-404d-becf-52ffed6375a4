import 'package:json_annotation/json_annotation.dart';

part 'category_resp.g.dart';

@JsonSerializable()
class CategoryResp {
  String? id;
  String? name;
  String? description;
  int? priority;
  String? categoryTypeId;

  CategoryResp({this.id, this.name, this.priority, this.description,this.categoryTypeId});

  factory CategoryResp.fromJson(Map<String, dynamic> json) {
    return _$CategoryRespFromJson(json);
  }

  Map<String, dynamic> toJson() => _$CategoryRespToJson(this);
}
