// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserResp _$UserR<PERSON>p<PERSON>rom<PERSON>(Map<String, dynamic> json) => UserResp(
      id: json['id'] as String?,
      username: json['username'] as String?,
      nickname: json['nickname'] as String?,
      status: (json['status'] as num?)?.toInt(),
      avatar: json['avatar'] as String?,
      roleName: json['roleName'] as String?,
    );

Map<String, dynamic> _$UserRespToJson(UserResp instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'nickname': instance.nickname,
      'status': instance.status,
      'avatar': instance.avatar,
      'roleName': instance.roleName,
    };
