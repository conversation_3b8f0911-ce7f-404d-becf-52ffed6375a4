import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'app/modules/home/<USER>/home_binding.dart';
import 'app/routes/app_pages.dart';
import 'common/constants.dart';
import 'config/config.dart';
import 'generated/locales.g.dart';
import 'theme/constants.dart';
import 'theme/theme_controller.dart';

void main() {
  SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
  SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  WidgetsFlutterBinding.ensureInitialized();

  if (kReleaseMode) {
    Net.configureDio(baseUrl: "https://test.seedtu.com");
  } else if (Config().isDev) {
    Net.configureDio(baseUrl: "http://127.0.0.1:3000/");
  } else {
    Net.configureDio(baseUrl: "http://127.0.0.1:3000/");
    // Net.configureDio(baseUrl: "https://test.seedtu.com");
  }
  Net.getRestClient().loginAdmin({
    'username': 'admin',
    'password': '19930118',
  }).then((value) async => {
        Net.getDio().options.headers['Authorization'] = value.data.token,
        runApp(const MyApp()),
      });
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.circle
    ..loadingStyle = EasyLoadingStyle.light
    ..radius = 16
    ..userInteractions = true
    ..dismissOnTap = false;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeController themeController = Get.put(ThemeController());
    return Obx(() => GetMaterialApp(
          debugShowCheckedModeBanner: false,
          title: appName,
          themeMode: themeController.themeMode.value,
          theme: ThemeData(
            colorSchemeSeed:
                themeController.colorSelectionMethod == ColorSelectionMethod.colorSeed ? themeController.colorSelected.value.color : null,
            colorScheme: themeController.colorSelectionMethod == ColorSelectionMethod.image ? themeController.imageColorScheme.value : null,
            useMaterial3: true,
            brightness: Brightness.light,
          ),
          darkTheme: ThemeData(
            colorSchemeSeed: themeController.colorSelectionMethod == ColorSelectionMethod.colorSeed
                ? themeController.colorSelected.value.color
                : themeController.imageColorScheme.value.primary,
            useMaterial3: true,
            brightness: Brightness.dark,
          ),
          // initialBinding: SplashBinding(),
          // initialRoute: AppPages.INITIAL,
          initialBinding: HomeBinding(),
          initialRoute: Routes.HOME,
          getPages: AppPages.routes,
          translationsKeys: AppTranslation.translations,
          locale: const Locale('zh', 'CN'),
          fallbackLocale: const Locale('en', 'US'),
          builder: EasyLoading.init(),
        ));
  }
}
