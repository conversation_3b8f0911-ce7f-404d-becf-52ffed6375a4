import 'package:delightful_toast/delight_toast.dart';
import 'package:delightful_toast/toast/components/toast_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

extension ToastExtension on String {
  void get toast => DelightToastBar(
        autoDismiss: true,
        snackbarDuration: const Duration(seconds: 2),
        animationDuration: const Duration(seconds: 1),
        builder: (BuildContext context) {
          return ToastCard(
            leading: const Icon(
              Icons.flutter_dash,
              size: 28,
            ),
            title: Text(
              this,
              style: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 14,
              ),
            ),
          );
        },
      ).show(Get.context!);
}
