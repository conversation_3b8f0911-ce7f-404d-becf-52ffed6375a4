import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;
import 'package:json_annotation/json_annotation.dart';
import 'package:lsenglish_admin/util/pair.dart';

import '../models/ted_source_resp/language.dart';
import '../models/ted_source_resp/subtitle.dart';
import '../models/ted_source_resp/ted_source_resp.dart';
part 'ted.g.dart';

@JsonSerializable()
class AllData {
  final Props props;

  AllData({required this.props});

  factory AllData.fromJson(Map<String, dynamic> json) => _$AllDataFromJson(json);
  Map<String, dynamic> toJson() => _$AllDataToJson(this);
}

@JsonSerializable()
class Props {
  final PageProps pageProps;

  Props({required this.pageProps});

  factory Props.fromJson(Map<String, dynamic> json) => _$PropsFromJson(json);
  Map<String, dynamic> toJson() => _$PropsToJson(this);
}

@JsonSerializable()
class PageProps {
  final VideoData videoData;
  final String shortenedUrl;

  PageProps({required this.videoData, required this.shortenedUrl});

  factory PageProps.fromJson(Map<String, dynamic> json) => _$PagePropsFromJson(json);
  Map<String, dynamic> toJson() => _$PagePropsToJson(this);
}

@JsonSerializable()
class VideoData {
  final String id;
  final String playerData;
  final String description;
  final String presenterDisplayName;
  final String title;
  final String socialTitle;
  final String publishedAt;
  final int duration;
  final int viewedCount;

  VideoData({
    required this.id,
    required this.playerData,
    required this.description,
    required this.presenterDisplayName,
    required this.title,
    required this.socialTitle,
    required this.publishedAt,
    required this.duration,
    required this.viewedCount,
  });

  factory VideoData.fromJson(Map<String, dynamic> json) => _$VideoDataFromJson(json);
  Map<String, dynamic> toJson() => _$VideoDataToJson(this);
}

@JsonSerializable()
class PlayerData {
  final Resources resources;
  final String canonical;
  final String thumb;
  final String slug;
  final List<TedSourceLanguage> languages;
  final PlayerDataExternal external;

  PlayerData({
    required this.resources,
    required this.canonical,
    required this.thumb,
    required this.slug,
    required this.languages,
    required this.external,
  });

  factory PlayerData.fromJson(Map<String, dynamic> json) => _$PlayerDataFromJson(json);
  Map<String, dynamic> toJson() => _$PlayerDataToJson(this);
}

@JsonSerializable()
class PlayerDataExternal {
  final String code;
  final String service;

  PlayerDataExternal({required this.code, required this.service});

  factory PlayerDataExternal.fromJson(Map<String, dynamic> json) => _$PlayerDataExternalFromJson(json);
  Map<String, dynamic> toJson() => _$PlayerDataExternalToJson(this);
}

@JsonSerializable()
class Resources {
  final HLS hls;
  final List<H264> h264;

  Resources({required this.hls, required this.h264});

  factory Resources.fromJson(Map<String, dynamic> json) => _$ResourcesFromJson(json);
  Map<String, dynamic> toJson() => _$ResourcesToJson(this);
}

@JsonSerializable()
class HLS {
  final String stream;
  final String metadata;

  HLS({required this.stream, required this.metadata});

  factory HLS.fromJson(Map<String, dynamic> json) => _$HLSFromJson(json);
  Map<String, dynamic> toJson() => _$HLSToJson(this);
}

@JsonSerializable()
class H264 {
  final String file;

  H264({required this.file});

  factory H264.fromJson(Map<String, dynamic> json) => _$H264FromJson(json);
  Map<String, dynamic> toJson() => _$H264ToJson(this);
}

@JsonSerializable()
class Metadata {
  final List<TedSourceSubtitle> subtitles;

  Metadata({required this.subtitles});

  factory Metadata.fromJson(Map<String, dynamic> json) => _$MetadataFromJson(json);
  Map<String, dynamic> toJson() => _$MetadataToJson(this);
}

Future<TedSourceResp?> fetchTedVideo(String url) async {
  Dio dio = Dio();

  final response = await dio.get(url);
  if (response.statusCode == 200) {
    dom.Document document = html_parser.parse(response.data);
    dom.Element? scriptTag = document.getElementById("__NEXT_DATA__");
    if (scriptTag != null) {
      String jsonData = scriptTag.text;
      final allData = AllData.fromJson(jsonDecode(jsonData));
      final videoData = allData.props.pageProps.videoData;
      final playerData = PlayerData.fromJson(jsonDecode(videoData.playerData));

      // 获取 MP4 URL
      String? mp4Url;
      if (playerData.resources.h264.isNotEmpty) {
        mp4Url = playerData.resources.h264.first.file;
      }
      // 获取 HLS Metadata
      final hlsResponse = await dio.get(playerData.resources.hls.metadata);
      if (hlsResponse.statusCode == 200) {
        final metadata = Metadata.fromJson(hlsResponse.data);
        TedSourceResp tedResp = TedSourceResp(
            id: videoData.id,
            shortenedUrl: allData.props.pageProps.shortenedUrl,
            description: videoData.description,
            playerUrl: mp4Url, // 确认这个变量已经定义并且包含了正确的 URL
            presenterDisplayName: videoData.presenterDisplayName,
            title: videoData.title,
            socialTitle: videoData.socialTitle,
            publishedAt: videoData.publishedAt,
            duration: videoData.duration,
            viewedCount: videoData.viewedCount,
            canonical: playerData.canonical,
            thumb: playerData.thumb,
            slug: playerData.slug,
            m3u8: playerData.resources.hls.stream,
            metadata: playerData.resources.hls.metadata,
            subtitles: metadata.subtitles, // 确认这个 metadata 已经被解析并包含了字幕信息
            languages: playerData.languages,
            youTubeUrl: "https://www.youtube.com/watch?v=${playerData.external.code}");
        return tedResp;
      } else {
        return null;
      }
    } else {
      print("Script tag not found");
      return null;
    }
  } else {
    print("Failed to load the page");
    return null;
  }
}

Future<Pair<String, String>> fetechTitleAndDesc(String url) async {
  Dio dio = Dio();
  final resp = await dio.get(url);
  if (resp.statusCode == 200) {
    return Pair(resp.data['title'], resp.data['description']);
  }
  return Pair("", "");
}
