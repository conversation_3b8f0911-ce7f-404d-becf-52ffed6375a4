import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class ImageLoader extends StatelessWidget {
  final String url;
  final BoxFit fit;
  final double? width;
  final double? height;
  final Color? color;
  final double? size;
  final Uint8List? bytes;

  // 工厂构造函数
  factory ImageLoader(
    String url, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Color? color,
    double? size,
    Uint8List? bytes,
    // 其他参数...
  }) {
    return ImageLoader._load(
      url: url,
      fit: fit,
      width: width,
      height: height,
      color: color,
      size: size,
      bytes: bytes,
      // 其他参数...
    );
  }

  // 私有构造函数用于网络图片
  ImageLoader._load({
    required this.url,
    required this.fit,
    this.width,
    this.height,
    this.color,
    this.size,
    this.bytes,
    // 其他参数...
  }) : super(key: Unique<PERSON>ey());

  @override
  Widget build(BuildContext context) {
    if (url.isEmpty && bytes != null) {
      return ExtendedImage.memory(
        bytes!,
        fit: fit,
        width: size ?? width,
        height: size ?? height,
        color: color,
      );
    } else if (url.startsWith('http') || url.startsWith('https')) {
      // 返回网络图片
      return ExtendedImage.network(
        url,
        fit: fit,
        width: size ?? width,
        height: size ?? height,
        color: color,
      );
    } else if (url.startsWith('file')) {
      // 返回网络图片
      return Image.file(
        File(url),
        fit: fit,
        width: size ?? width,
        height: size ?? height,
        color: color,
      );
    } else if (url.toLowerCase().endsWith('.svg')) {
      return SvgPicture.asset(
        url,
        fit: fit,
        width: size ?? width,
        height: size ?? height,
        color: color,
      );
    } else {
      // 返回资源图片
      return Image.asset(
        url,
        fit: fit,
        width: size ?? width,
        height: size ?? height,
        color: color,
        // 其他参数...
      );
    }
  }
}
