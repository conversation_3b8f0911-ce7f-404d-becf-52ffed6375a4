// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ted.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AllData _$AllDataFromJson(Map<String, dynamic> json) => AllData(
      props: Props.fromJson(json['props'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AllDataToJson(AllData instance) => <String, dynamic>{
      'props': instance.props,
    };

Props _$PropsFromJson(Map<String, dynamic> json) => Props(
      pageProps: PageProps.fromJson(json['pageProps'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropsToJson(Props instance) => <String, dynamic>{
      'pageProps': instance.pageProps,
    };

PageProps _$PagePropsFromJson(Map<String, dynamic> json) => PageProps(
      videoData: VideoData.fromJson(json['videoData'] as Map<String, dynamic>),
      shortenedUrl: json['shortenedUrl'] as String,
    );

Map<String, dynamic> _$PagePropsToJson(PageProps instance) => <String, dynamic>{
      'videoData': instance.videoData,
      'shortenedUrl': instance.shortenedUrl,
    };

VideoData _$VideoDataFromJson(Map<String, dynamic> json) => VideoData(
      id: json['id'] as String,
      playerData: json['playerData'] as String,
      description: json['description'] as String,
      presenterDisplayName: json['presenterDisplayName'] as String,
      title: json['title'] as String,
      socialTitle: json['socialTitle'] as String,
      publishedAt: json['publishedAt'] as String,
      duration: (json['duration'] as num).toInt(),
      viewedCount: (json['viewedCount'] as num).toInt(),
    );

Map<String, dynamic> _$VideoDataToJson(VideoData instance) => <String, dynamic>{
      'id': instance.id,
      'playerData': instance.playerData,
      'description': instance.description,
      'presenterDisplayName': instance.presenterDisplayName,
      'title': instance.title,
      'socialTitle': instance.socialTitle,
      'publishedAt': instance.publishedAt,
      'duration': instance.duration,
      'viewedCount': instance.viewedCount,
    };

PlayerData _$PlayerDataFromJson(Map<String, dynamic> json) => PlayerData(
      resources: Resources.fromJson(json['resources'] as Map<String, dynamic>),
      canonical: json['canonical'] as String,
      thumb: json['thumb'] as String,
      slug: json['slug'] as String,
      languages: (json['languages'] as List<dynamic>)
          .map((e) => TedSourceLanguage.fromJson(e as Map<String, dynamic>))
          .toList(),
      external:
          PlayerDataExternal.fromJson(json['external'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PlayerDataToJson(PlayerData instance) =>
    <String, dynamic>{
      'resources': instance.resources,
      'canonical': instance.canonical,
      'thumb': instance.thumb,
      'slug': instance.slug,
      'languages': instance.languages,
      'external': instance.external,
    };

PlayerDataExternal _$PlayerDataExternalFromJson(Map<String, dynamic> json) =>
    PlayerDataExternal(
      code: json['code'] as String,
      service: json['service'] as String,
    );

Map<String, dynamic> _$PlayerDataExternalToJson(PlayerDataExternal instance) =>
    <String, dynamic>{
      'code': instance.code,
      'service': instance.service,
    };

Resources _$ResourcesFromJson(Map<String, dynamic> json) => Resources(
      hls: HLS.fromJson(json['hls'] as Map<String, dynamic>),
      h264: (json['h264'] as List<dynamic>)
          .map((e) => H264.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ResourcesToJson(Resources instance) => <String, dynamic>{
      'hls': instance.hls,
      'h264': instance.h264,
    };

HLS _$HLSFromJson(Map<String, dynamic> json) => HLS(
      stream: json['stream'] as String,
      metadata: json['metadata'] as String,
    );

Map<String, dynamic> _$HLSToJson(HLS instance) => <String, dynamic>{
      'stream': instance.stream,
      'metadata': instance.metadata,
    };

H264 _$H264FromJson(Map<String, dynamic> json) => H264(
      file: json['file'] as String,
    );

Map<String, dynamic> _$H264ToJson(H264 instance) => <String, dynamic>{
      'file': instance.file,
    };

Metadata _$MetadataFromJson(Map<String, dynamic> json) => Metadata(
      subtitles: (json['subtitles'] as List<dynamic>)
          .map((e) => TedSourceSubtitle.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MetadataToJson(Metadata instance) => <String, dynamic>{
      'subtitles': instance.subtitles,
    };
