String getFileExtensionFromUrl(String url) {
  Uri uri = Uri.parse(url);
  String path = uri.path; // 获取 uri 的路径部分，这部分通常包含文件名和后缀

  // 查找最后一个点后面的内容作为文件扩展名
  int dotIndex = path.lastIndexOf('.');
  if (dotIndex != -1 && dotIndex < path.length - 1) {
    return path.substring(dotIndex + 1);
  }
  return '';
}

String removeLastQuestionMark(String input) {
  if (input.endsWith('?')) {
    return input.substring(0, input.length - 1);
  }
  return input;
}
