import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:path_provider/path_provider.dart';

class DownloadProgress {
  int totalSize;
  List<int> receivedSizes;
  DateTime lastTime;
  int lastReceived = 0;
  double speed = 0; // 初始化速度

  Function(int, int, double) onProgressUpdate;

  DownloadProgress(this.totalSize, int chunksCount, this.onProgressUpdate)
      : receivedSizes = List.filled(chunksCount, 0),
        lastTime = DateTime.now();

  void updateChunk(int chunkIndex, int receivedSize) {
    receivedSizes[chunkIndex] = receivedSize;
    int totalReceived = receivedSizes.fold(0, (sum, x) => sum + x);

    DateTime currentTime = DateTime.now();
    double timeDiff = currentTime.difference(lastTime).inSeconds.toDouble();

    if (timeDiff >= 1) {
      // 至少每秒更新一次速度
      speed = (totalReceived - lastReceived) / timeDiff; // Bytes per second
      lastReceived = totalReceived;
      lastTime = currentTime;

      onProgressUpdate(totalReceived, totalSize, speed);
    }
  }
}

/// 下载文件，并通过回调传递进度。
///
/// [url] 文件的网络URL。
/// [filename] 要保存的文件名。
/// [onProgress] 下载进度回调，传递当前进度和总进度。
Future<String?> downloadFile(String url, String filename, {void Function(int, int)? onProgress}) async {
  debugPrint("开始下载文件:$url");
  try {
    Directory directory = await getApplicationDocumentsDirectory();
    String filePath = '${directory.path}/$filename';

    await Net.getDownloadDio().download(url, filePath, onReceiveProgress: (received, total) {
      // 调用外部传进的进度回调
      onProgress?.call(received, total);
    });

    debugPrint("downloadFile filePath = $filePath");
    return filePath;
  } catch (e) {
    debugPrint("downloadFile error = $e");
    return null;
  }
}

Future<void> downloadChunk(String url, String savePath, int start, int end, int chunkIndex, DownloadProgress progress) async {
  try {
    Response<List<int>> response = await Net.getDownloadDio().get<List<int>>(
      url,
      options: Options(
        responseType: ResponseType.bytes,
        followRedirects: false,
        headers: {'Range': 'bytes=$start-$end'},
      ),
      onReceiveProgress: (received, total) {
        progress.updateChunk(chunkIndex, received);
      },
    );
    File file = File(savePath);
    var raf = file.openSync(mode: FileMode.writeOnlyAppend);
    raf.setPositionSync(start);
    raf.writeFromSync(response.data!);
    raf.close();
  } catch (e) {
    print('Error downloading chunk: $e');
  }
}

Future<String> downloadLargeFile(String url, String filename, {required void Function(int, int, double) onProgress}) async {
  Directory directory = await getApplicationDocumentsDirectory();
  String filePath = '${directory.path}/$filename';

  int fileSize = await getFileSize(url);
  int chunkSize = 1000000; // 例如，每个块 1 MB
  int chunksCount = (fileSize / chunkSize).ceil();

  DownloadProgress progress = DownloadProgress(fileSize, chunksCount, onProgress);

  List<Future> futures = [];
  for (int i = 0; i < fileSize; i += chunkSize) {
    int start = i;
    int end = (i + chunkSize > fileSize) ? fileSize - 1 : i + chunkSize - 1;
    if (end >= fileSize) end = fileSize - 1;
    int chunkIndex = (i / chunkSize).floor();
    futures.add(downloadChunk(url, filePath, start, end, chunkIndex, progress));
  }

  await Future.wait(futures);
  print('Download completed');
  return filePath;
}

Future<int> getFileSize(String url) async {
  try {
    Response response = await Net.getDownloadDio().head(url);
    if (response.headers.value('Content-Length') != null) {
      return int.parse(response.headers.value('Content-Length')!);
    } else {
      throw Exception("Failed to get content length.");
    }
  } catch (e) {
    print('Error getting file size: $e');
    rethrow;
  }
}

Future<String> getAndDeleteFile(String filename) async {
  try {
    // 获取应用文档目录
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/$filename';

    // 创建文件实例
    final file = File(filePath);

    // 检查文件是否存在
    if (await file.exists()) {
      // 如果存在，则删除文件
      await file.delete();
      print("File deleted successfully.");
    } else {
      print("File does not exist.");
    }
    return filePath;
  } catch (e) {
    print("Error occurred while deleting the file: $e");
    return "";
  }
}

Future<void> deleteFile(String path) async {
  try {
    await File(path).delete();
  } catch (e) {
    throw Exception('Failed to delete the file: $e');
  }
}
