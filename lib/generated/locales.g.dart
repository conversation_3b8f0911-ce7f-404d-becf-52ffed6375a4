// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars
// ignore: avoid_classes_with_only_static_members
class AppTranslation {
  static Map<String, Map<String, String>> translations = {
    'zh_CN': Locales.zh_CN,
    'en_US': Locales.en_US,
  };
}

class LocaleKeys {
  LocaleKeys._();
  static const drawer_home = 'drawer_home';
  static const drawer_user_manager = 'drawer_user_manager';
  static const drawer_role_manager = 'drawer_role_manager';
  static const drawer_permission_manager = 'drawer_permission_manager';
  static const drawer_user_title = 'drawer_user_title';
  static const drawer_permission = 'drawer_permission';
  static const drawer_permission_page = 'drawer_permission_page';
  static const drawer_permission_role = 'drawer_permission_role';
  static const drawer_system_setting = 'drawer_system_setting';
  static const drawer_scheduled_tasks = 'drawer_scheduled_tasks';
  static const drawer_theme_setting = 'drawer_theme_setting';
  static const drawer_locales_setting = 'drawer_locales_setting';
  static const common_login = 'common_login';
  static const common_logout = 'common_logout';
  static const tooltip_toggle_brightness = 'tooltip_toggle_brightness';
  static const tooltip_select_color_seed = 'tooltip_select_color_seed';
  static const tooltip_select_color_images = 'tooltip_select_color_images';
  static const user_name = 'user_name';
  static const user_role = 'user_role';
  static const user_create_time = 'user_create_time';
  static const user_status = 'user_status';
  static const operation_modify = 'operation_modify';
  static const operation_delete = 'operation_delete';
  static const net_connectionTimeout = 'net_connectionTimeout';
  static const net_sendTimeout = 'net_sendTimeout';
  static const net_receiveTimeout = 'net_receiveTimeout';
  static const net_badResponse = 'net_badResponse';
  static const net_cancel = 'net_cancel';
  static const net_connectionError = 'net_connectionError';
  static const net_400 = 'net_400';
  static const net_401 = 'net_401';
  static const net_403 = 'net_403';
  static const net_404 = 'net_404';
  static const net_408 = 'net_408';
  static const net_500 = 'net_500';
  static const net_501 = 'net_501';
  static const net_502 = 'net_502';
  static const net_503 = 'net_503';
  static const net_504 = 'net_504';
  static const net_505 = 'net_505';
  static const net_default_code_error = 'net_default_code_error';
  static const input_user_name_label = 'input_user_name_label';
  static const input_user_name_error = 'input_user_name_error';
  static const input_password_label = 'input_password_label';
  static const input_password_error = 'input_password_error';
}

class Locales {
  static const zh_CN = {
    'drawer_home': '主页',
    'drawer_user_manager': '用户管理',
    'drawer_role_manager': '角色管理',
    'drawer_permission_manager': '权限管理',
    'drawer_user_title': '用户',
    'drawer_permission': '权限',
    'drawer_permission_page': '页面权限',
    'drawer_permission_role': '角色权限',
    'drawer_system_setting': '系统设置',
    'drawer_scheduled_tasks': '定时任务',
    'drawer_theme_setting': '主题设置',
    'drawer_locales_setting': '多语言',
    'common_login': '登录',
    'common_logout': '登出',
    'tooltip_toggle_brightness': '切换亮色暗色',
    'tooltip_select_color_seed': '选择主题色',
    'tooltip_select_color_images': '选择颜色提取图像',
    'user_name': '用户名称',
    'user_role': '用户角色',
    'user_create_time': '创建时间',
    'user_status': '状态',
    'operation_modify': '修改',
    'operation_delete': '删除',
    'net_connectionTimeout': '网络连接超时，请检查网络设置',
    'net_sendTimeout': '服务器异常，请稍后重试！',
    'net_receiveTimeout': '网络连接超时，请检查网络设置',
    'net_badResponse': '服务器异常，请稍后重试！',
    'net_cancel': '请求已被取消，请重新请求',
    'net_connectionError': '网络异常，请稍后重试！',
    'net_400': '请求语法错误',
    'net_401': '未授权，请登录',
    'net_403': '拒绝访问',
    'net_404': '请求地址404',
    'net_408': '请求超时',
    'net_500': '服务器异常',
    'net_501': '服务未实现',
    'net_502': '网关错误',
    'net_503': '服务不可用',
    'net_504': '网关超时',
    'net_505': 'HTTP版本不受支持',
    'net_default_code_error': '请求失败，错误码',
    'input_user_name_label': '用户名',
    'input_user_name_error': '请输入用户名',
    'input_password_label': '密码',
    'input_password_error': '请输入密码',
  };
  static const en_US = {
    'drawer_home': 'Dashboards',
    'drawer_user_manager': 'User Manager',
    'drawer_role_manager': 'Role Manager',
    'drawer_permission_manager': 'Permission Manager',
    'drawer_user_title': 'User',
    'drawer_permission': 'Permission',
    'drawer_permission_page': 'Page Permission',
    'drawer_permission_role': 'Role Permission',
    'drawer_system_setting': 'System Setting',
    'drawer_scheduled_tasks': 'Scheduled Task',
    'drawer_theme_setting': 'Theme Setting',
    'drawer_locales_setting': 'Multi-Language',
    'common_login': 'Login',
    'common_logout': 'Logout',
    'tooltip_toggle_brightness': 'Toggle brightness',
    'tooltip_select_color_seed': 'Select a seed color',
    'tooltip_select_color_images': 'Select a color extraction image',
    'user_name': 'UserName',
    'user_role': 'UserRole',
    'user_create_time': 'CreateTime',
    'user_status': 'Status',
    'operation_modify': 'Modify',
    'operation_delete': 'Delete',
    'net_connectionTimeout': 'Connection Timeout',
    'net_sendTimeout': 'Server exception, please try again later!',
    'net_receiveTimeout': '网络连接超时，请检查网络设置',
    'net_badResponse':
        'Network connection timed out, please check network settings',
    'net_cancel': 'The request has been canceled, please request again',
    'net_connectionError': 'Network abnormality, please try again later!',
    'net_400': 'Request syntax error',
    'net_401': 'Unauthorized, please log in',
    'net_403': 'Access Denied',
    'net_404': 'Request address 404',
    'net_408': 'Request timed out',
    'net_500': 'Server exception',
    'net_501': 'Service not implemented',
    'net_502': 'Gateway error',
    'net_503': 'Service is not available',
    'net_504': 'Gateway timeout',
    'net_505': 'HTTP version is not supported',
    'net_default_code_error': 'Request failed, error code',
    'input_user_name_label': 'UserName',
    'input_user_name_error': 'Please input username',
    'input_password_label': 'Password',
    'input_password_error': 'Please input password',
  };
}
