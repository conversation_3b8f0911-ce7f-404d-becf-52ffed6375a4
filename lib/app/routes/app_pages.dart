import 'package:get/get.dart';

import '../modules/category/bindings/category_binding.dart';
import '../modules/category/views/category_view.dart';
import '../modules/category_type/bindings/category_type_binding.dart';
import '../modules/category_type/views/category_type_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/promotion_code/bindings/promotion_code_binding.dart';
import '../modules/promotion_code/views/promotion_code_view.dart';
import '../modules/series/bindings/series_binding.dart';
import '../modules/series/views/series_view.dart';
import '../modules/splash/splash_binding.dart';
import '../modules/splash/splash_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.CATEGORY,
      page: () => const CategoryView(),
      binding: CategoryBinding(),
    ),
    GetPage(
      name: _Paths.CATEGORY_TYPE,
      page: () => const CategoryTypeView(),
      binding: CategoryTypeBinding(),
    ),
    GetPage(
      name: _Paths.SERIES,
      page: () => const SeriesView(),
      binding: SeriesBinding(),
      children: [
        GetPage(
          name: _Paths.SERIES,
          page: () => const SeriesView(),
          binding: SeriesBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.PROMOTION_CODE,
      page: () => const PromotionCodeView(),
      binding: PromotionCodeBinding(),
    ),
  ];
}
