import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/home/<USER>/home_controller.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';

class DrawerItem {
  const DrawerItem(this.label, this.icon, this.selectedIcon);

  final String label;
  final Widget icon;
  final Widget selectedIcon;
}

// 从统一配置获取各分类的菜单项
List<DrawerItem> get userDestinations => HomeController.menuConfigs
    .where((config) => config.category == "user")
    .map((config) => DrawerItem(config.label, config.icon, config.selectedIcon))
    .toList();

List<DrawerItem> get sourceDestinations => HomeController.menuConfigs
    .where((config) => config.category == "source")
    .map((config) => DrawerItem(config.label, config.icon, config.selectedIcon))
    .toList();

List<DrawerItem> get vipDestinations => HomeController.menuConfigs
    .where((config) => config.category == "vip")
    .map((config) => DrawerItem(config.label, config.icon, config.selectedIcon))
    .toList();

// 保留原有的权限和系统设置菜单（这些暂时不在统一配置中）
List<DrawerItem> permissionDestinations = <DrawerItem>[
  DrawerItem(LocaleKeys.drawer_permission_page.tr, const Icon(Icons.find_in_page_outlined), const Icon(Icons.find_in_page)),
  DrawerItem(LocaleKeys.drawer_permission_role.tr, const Icon(Icons.verified_user_outlined), const Icon(Icons.verified_user))
];

List<DrawerItem> systemDestinations = <DrawerItem>[
  DrawerItem(LocaleKeys.drawer_scheduled_tasks.tr, const Icon(Icons.schedule_outlined), const Icon(Icons.schedule)),
  DrawerItem(LocaleKeys.drawer_theme_setting.tr, const Icon(Icons.dark_mode_outlined), const Icon(Icons.dark_mode)),
  DrawerItem(LocaleKeys.drawer_locales_setting.tr, const Icon(Icons.translate_outlined), const Icon(Icons.translate))
];

class SideMenu extends StatelessWidget {
  const SideMenu({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HomeController homeController = Get.find();

    // 获取首页菜单项
    final homeItems = HomeController.menuConfigs
        .where((config) => config.category == "home")
        .toList();

    return Obx(
      () => NavigationDrawer(
        onDestinationSelected: homeController.handleScreenChanged,
        selectedIndex: homeController.screenIndex.value,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Row(
              children: [const Icon(Icons.abc, size: defaultPadding * 4), Text(appName, style: Theme.of(context).textTheme.titleLarge)],
            ),
          ),
          // 动态生成首页菜单项
          ...homeItems.map((config) => NavigationDrawerDestination(
            label: Text(config.label),
            icon: config.icon,
            selectedIcon: config.selectedIcon,
          )),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              LocaleKeys.drawer_user_title.tr,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...userDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              "资源",
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...sourceDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              "会员",
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...vipDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              LocaleKeys.drawer_permission.tr,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...permissionDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              LocaleKeys.drawer_system_setting.tr,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...systemDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
        ],
      ),
    );
  }
}
