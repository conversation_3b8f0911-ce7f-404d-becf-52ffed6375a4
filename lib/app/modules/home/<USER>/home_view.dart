import 'dart:math';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/theme/theme_controller.dart';
import 'package:lsenglish_admin/theme/theme_widget.dart';
import 'package:lsenglish_admin/util/responsive.dart';

import '../components/side_menu.dart';
import '../controllers/home_controller.dart';

ThemeController themeController = Get.find();

class HomeView extends GetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: controller.homeDrawerKey,
      drawer: const SideMenu(),
      body: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Visibility(
                visible: Responsive.isDesktop(context),
                child: const Expanded(
                  child: SideMenu(),
                )),
            Expanded(
              // It takes 5/6 part of the screen
              flex: 5,
              child: Safe<PERSON>rea(
                child: Padding(
                  padding: const EdgeInsets.only(left: defaultPadding * 2, right: defaultPadding * 2),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Visibility(
                              visible: !Responsive.isDesktop(context),
                              child: IconButton(
                                icon: const Icon(Icons.menu),
                                onPressed: controller.openDrawer,
                              )),
                          Padding(
                            padding: const EdgeInsets.all(defaultPadding),
                            child: SizedBox(
                              width: min(Get.width * 0.4, 500),
                              child: SearchAnchor(builder: (BuildContext context, SearchController controller) {
                                return SearchBar(
                                  controller: controller,
                                  padding: const WidgetStatePropertyAll<EdgeInsets>(EdgeInsets.symmetric(horizontal: 16.0)),
                                  onTap: () {
                                    controller.openView();
                                  },
                                  onChanged: (_) {
                                    controller.openView();
                                  },
                                  leading: const Icon(Icons.search),
                                );
                              }, suggestionsBuilder: (BuildContext context, SearchController controller) {
                                return List<ListTile>.generate(5, (int index) {
                                  final String item = 'item $index';
                                  return ListTile(
                                    title: Text(item),
                                    onTap: () {
                                      controller.closeView(item);
                                    },
                                  );
                                });
                              }),
                            ),
                          ),
                          const Spacer(),
                          ...getActionChildren()
                        ],
                      ),
                      Expanded(
                        child: Builder(
                          builder: (BuildContext context) {
                            return Obx(() {
                              return controller.getPanelWidget();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
