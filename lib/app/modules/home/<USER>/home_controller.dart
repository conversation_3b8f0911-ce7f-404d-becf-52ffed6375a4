import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/benefit/controllers/benefit_controller.dart';
import 'package:lsenglish_admin/app/modules/benefit/views/benefit_view.dart';
import 'package:lsenglish_admin/app/modules/benefit_group/controllers/benefit_group_controller.dart';
import 'package:lsenglish_admin/app/modules/benefit_group/views/benefit_group_view.dart';
import 'package:lsenglish_admin/app/modules/user_benefit/controllers/user_benefit_controller.dart';
import 'package:lsenglish_admin/app/modules/user_benefit/views/user_benefit_view.dart';
import 'package:lsenglish_admin/app/modules/dashboard/views/dashboard_view.dart';
import 'package:lsenglish_admin/app/modules/category/controllers/category_controller.dart';
import 'package:lsenglish_admin/app/modules/category/views/category_view.dart';
import 'package:lsenglish_admin/app/modules/category_type/controllers/category_type_controller.dart';
import 'package:lsenglish_admin/app/modules/category_type/views/category_type_view.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/controllers/promotion_code_controller.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/views/promotion_code_view.dart';
import 'package:lsenglish_admin/app/modules/resource/controllers/resource_controller.dart';
import 'package:lsenglish_admin/app/modules/resource/views/resource_view.dart';
import 'package:lsenglish_admin/app/modules/series/controllers/series_controller.dart';
import 'package:lsenglish_admin/app/modules/series/views/series_view.dart';
import 'package:lsenglish_admin/app/modules/users/controllers/users_controller.dart';
import 'package:lsenglish_admin/app/modules/users/views/users_view.dart';

// 统一的菜单项配置
class MenuItemConfig {
  final String label;
  final Widget icon;
  final Widget selectedIcon;
  final Widget Function() viewBuilder;
  final Function()? controllerInitializer;
  final String category; // 用于分组

  MenuItemConfig({
    required this.label,
    required this.icon,
    required this.selectedIcon,
    required this.viewBuilder,
    this.controllerInitializer,
    required this.category,
  });
}

class PanelConfig {
  final Widget Function() viewBuilder;
  final Function()? controllerInitializer;

  PanelConfig({required this.viewBuilder, this.controllerInitializer});
}

class HomeController extends GetxController with GetSingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> homeDrawerKey = GlobalKey();

  var screenIndex = 0.obs;

  final Map<int, Widget> panelList = HashMap();

  // 统一的菜单配置 - 只需要在这里添加新模块
  static List<MenuItemConfig> get menuConfigs => [
    // 首页
    MenuItemConfig(
      label: "首页",
      icon: const Icon(Icons.widgets),
      selectedIcon: const Icon(Icons.widgets_outlined),
      viewBuilder: () => const DashboardView(),
      category: "home",
    ),

    // 用户管理
    MenuItemConfig(
      label: "用户管理",
      icon: const Icon(Icons.account_circle_outlined),
      selectedIcon: const Icon(Icons.account_circle),
      viewBuilder: () => const UsersView(),
      controllerInitializer: () => Get.put(UsersController(), permanent: true),
      category: "user",
    ),

    // 资源管理
    MenuItemConfig(
      label: "分类类型管理",
      icon: const Icon(Icons.label_outlined),
      selectedIcon: const Icon(Icons.label),
      viewBuilder: () => const CategoryTypeView(),
      controllerInitializer: () => Get.put(CategoryTypeController(), permanent: true),
      category: "source",
    ),
    MenuItemConfig(
      label: "分类管理",
      icon: const Icon(Icons.category_outlined),
      selectedIcon: const Icon(Icons.category),
      viewBuilder: () => const CategoryView(),
      controllerInitializer: () => Get.put(CategoryController(), permanent: true),
      category: "source",
    ),
    MenuItemConfig(
      label: "剧集管理",
      icon: const Icon(Icons.movie_creation_outlined),
      selectedIcon: const Icon(Icons.movie),
      viewBuilder: () => const SeriesView(),
      controllerInitializer: () => Get.put(SeriesController(), permanent: true),
      category: "source",
    ),
    MenuItemConfig(
      label: "资源管理",
      icon: const Icon(Icons.group_outlined),
      selectedIcon: const Icon(Icons.group),
      viewBuilder: () => const ResourceView(),
      controllerInitializer: () => Get.put(ResourceController(), permanent: true),
      category: "source",
    ),

    // 会员管理
    MenuItemConfig(
      label: "兑换码",
      icon: const Icon(Icons.qr_code_scanner),
      selectedIcon: const Icon(Icons.qr_code),
      viewBuilder: () => const PromotionCodeView(),
      controllerInitializer: () => Get.put(PromotionCodeController(), permanent: true),
      category: "vip",
    ),
    MenuItemConfig(
      label: "权益组管理",
      icon: const Icon(Icons.group_work_outlined),
      selectedIcon: const Icon(Icons.group_work),
      viewBuilder: () => const BenefitGroupView(),
      controllerInitializer: () => Get.put(BenefitGroupController(), permanent: true),
      category: "vip",
    ),
    MenuItemConfig(
      label: "权益管理",
      icon: const Icon(Icons.card_giftcard_outlined),
      selectedIcon: const Icon(Icons.card_giftcard),
      viewBuilder: () => const BenefitView(),
      controllerInitializer: () => Get.put(BenefitController(), permanent: true),
      category: "vip",
    ),
    MenuItemConfig(
      label: "用户权益管理",
      icon: const Icon(Icons.person_pin_outlined),
      selectedIcon: const Icon(Icons.person_pin),
      viewBuilder: () => const UserBenefitView(),
      controllerInitializer: () => Get.put(UserBenefitController(), permanent: true),
      category: "vip",
    ),
  ];

  // 从统一配置生成 panelConfigs
  List<PanelConfig> get panelConfigs => menuConfigs.map((config) =>
    PanelConfig(
      viewBuilder: config.viewBuilder,
      controllerInitializer: config.controllerInitializer,
    )
  ).toList();

  void openDrawer() {
    homeDrawerKey.currentState?.openDrawer();
  }

  void handleScreenChanged(int selectedScreen) {
    screenIndex.value = selectedScreen;
    homeDrawerKey.currentState?.closeDrawer();
  }

  Widget getPanelWidget() {
    if (panelList.containsKey(screenIndex.value)) {
      return panelList[screenIndex.value] ?? Container();
    } else {
      Widget target = _getPanelWidgetByIndex(screenIndex.value);
      panelList[screenIndex.value] = target;
      return target;
    }
  }

  Widget _getPanelWidgetByIndex(int index) {
    if (index < 0 || index >= panelConfigs.length) {
      return const Text("Empty");
    }
    var config = panelConfigs[index];

    // 初始化控制器（如果有）
    config.controllerInitializer?.call();

    // 创建并返回视图
    return config.viewBuilder();
  }
}
