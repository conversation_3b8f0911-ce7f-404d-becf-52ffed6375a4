import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';

import '../controllers/benefit_group_controller.dart';
import 'benefit_group_add_widget.dart';

class BenefitGroupView extends BasePageListView<BenefitGroupResp, BenefitGroupController> {
  const BenefitGroupView({Key? key}) : super(key: key);

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID"), fixedWidth: 80),
      DataColumn2(label: Text("组名")),
      DataColumn2(label: Text("组编号")),
      DataColumn2(label: Text("状态"), fixedWidth: 100),
      DataColumn2(label: Text("描述")),
      DataColumn2(label: Text("创建时间"), fixedWidth: 150),
      DataColumn2(label: Text("操作"), fixedWidth: 120),
    ];
  }

  @override
  void onAddPressed() async {
    await Get.dialog(const BenefitGroupAddWidget(), barrierDismissible: false);
  }

  @override
  String getTitle() {
    return "权益组管理";
  }

  @override
  List<DataCell> buildDataCells(BenefitGroupResp item, int index) {
    return [
      DataCell(Text(item.id.toString())),
      DataCell(Text(item.name ?? "--")),
      DataCell(Text(item.code ?? "--")),
      DataCell(
        Switch(
          value: item.status == 1,
          onChanged: (value) {
            controller.toggleStatus(item);
          },
        ),
      ),
      DataCell(Text(item.description ?? "--")),
      DataCell(Text(item.createdAt ?? "--")),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(
                padding: const EdgeInsets.only(right: defaultPadding * 2),
                child: Text(LocaleKeys.operation_modify.tr)
              ),
              onPressed: () {
                controller.updateSelect(item, index);
              },
            ),
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId(BenefitGroupResp item, int index) {
    return item.id?.toString();
  }

  @override
  DataRow2 buildDataRow(BenefitGroupResp item, int index) {
    return DataRow2(
      specificRowHeight: getSpecificRowHeight(),
      onDoubleTap: () {
        controller.updateSelect(item, index);
      },
      key: ValueKey<String?>(getModelId(item, index)),
      // 禁用选中功能 - 不设置 onSelectChanged
      cells: buildDataCells(item, index),
    );
  }
}
