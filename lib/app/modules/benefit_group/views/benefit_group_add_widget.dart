import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';
import '../controllers/benefit_group_controller.dart';

class BenefitGroupAddWidget extends StatefulWidget {
  final BenefitGroupResp? resp;
  const BenefitGroupAddWidget({super.key, this.resp});

  @override
  State<BenefitGroupAddWidget> createState() => _BenefitGroupAddWidgetState();
}

class _BenefitGroupAddWidgetState extends State<BenefitGroupAddWidget> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.resp != null) {
      nameController.text = widget.resp!.name ?? "";
      codeController.text = widget.resp!.code ?? "";
      descriptionController.text = widget.resp!.description ?? "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加权益组" : "编辑权益组"),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: "组名 *",
                border: OutlineInputBorder(),
              ),
            ),
            const Gap(16),
            TextField(
              controller: codeController,
              decoration: const InputDecoration(
                labelText: "组编号 *",
                border: OutlineInputBorder(),
              ),
            ),
            const Gap(16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: "描述",
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (nameController.text.isEmpty) {
      Get.snackbar("错误", "组名不能为空");
      return false;
    }
    if (codeController.text.isEmpty) {
      Get.snackbar("错误", "组编号不能为空");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    Map<String, dynamic> data = {
      'name': nameController.text,
      'code': codeController.text,
      'description': descriptionController.text,
    };

    if (widget.resp != null) {
      data['id'] = widget.resp!.id;
    }

    try {
      await Get.find<BenefitGroupController>().addEntity(data);
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    codeController.dispose();
    descriptionController.dispose();
    super.dispose();
  }
}
