import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/toast.dart';
import '../views/benefit_group_add_widget.dart';

class BenefitGroupController extends PageListController<BenefitGroupResp> {
  @override
  Future<PageListResp<List<BenefitGroupResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().benefitGroupList(page, size);
    return result.data;
  }

  @override
  void updateSelect(BenefitGroupResp item, int index) async {
    await Get.dialog(BenefitGroupAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex(BenefitGroupResp item, int index) async {
    // 权益组不允许删除
    Get.snackbar("提示", "权益组不允许删除");
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    if (data.containsKey('id') && data['id'] != null) {
      // 更新操作
      await Net.getRestClient().updateBenefitGroup(data);
    } else {
      // 添加操作
      await Net.getRestClient().addBenefitGroup(data);
    }
    refreshDatasource();
  }

  @override
  void deleteMulti(List<String> ids) {
    // 权益组不允许批量删除
    Get.snackbar("提示", "权益组不允许删除");
  }

  // 切换状态
  void toggleStatus(BenefitGroupResp item) async {
    int newStatus = item.status == 1 ? 0 : 1;
    Map<String, dynamic> data = {
      'id': item.id,
      'status': newStatus,
    };

    try {
      await Net.getRestClient().updateBenefitGroupStatus(data);
      refreshDatasource();
      "状态更新成功".toast;
    } catch (e) {
      e.toString().toast;
    }
  }
}
