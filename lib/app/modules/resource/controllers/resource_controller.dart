import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/resource_resp/resource_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';
import '../views/resource_add_widget.dart';

class ResourceController extends PageListController<ResourceResp> {
  var categoryList = <CategoryResp>[];
  var categorySelect = CategoryResp().obs;
  var categorySelectIndex = 0.obs;

  @override
  void onReady() {
    super.onReady();
    Net.getRestClient().categoryList(1, 0).then((value) => _handleCategories(value.data.data));
  }

  _handleCategories(List<CategoryResp> list) {
    categoryList.clear();
    categoryList.add(CategoryResp(id: "", name: "全部"));
    categoryList.addAll(list);
    categorySelect.value = categoryList.firstOrNull ?? CategoryResp();
    refreshDatasource();
  }

  void addNewOne() async {
    await Get.dialog(ResourceAddWidget(
      addSuccessCallback: () {
        refreshDatasource();
      },
    ), barrierDismissible: false);
  }

  @override
  void updateSelect(ResourceResp item, int index) async {
    await Get.dialog(
        ResourceAddWidget(
          resourceResp: item,
          addSuccessCallback: () {
            refreshDatasource();
          },
        ),
        barrierDismissible: false);
  }

  @override
  void deleteByIndex(ResourceResp item, int index) {
    Net.getRestClient().deleteResource(item.id ?? "").then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    await Net.getRestClient().addResource(data);
    refreshDatasource();
  }

  @override
  Future<PageListResp<List<ResourceResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().resourceList(categorySelect.value.id ?? "", page, size, sortSelect.value);
    return result.data;
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().deleteResourceMulti(ids).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  void setFeaturedContent(ResourceResp item) {
    Net.getRestClient()
        .setResourceFeaturedContent({'id': item.id})
        .then((value) => refreshDatasource())
        .catchError((value) => value.toString().toast);
  }

  void setPriority(ResourceResp item) {
    CommonInputDialog(
      title: "修改优先级(越大优先级越高)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.priority.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          await Net.getRestClient()
              .setResourcePriority({'id': item.id, 'priority': int.parse(content)})
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }

  // 获取资源标签
  Future<List<String>> getResourceTags(String resourceId) async {
    try {
      var result = await Net.getRestClient().getResourceTags(resourceId);
      return result.data;
    } catch (e) {
      e.toString().toast;
      return [];
    }
  }

  // 设置资源标签
  Future<bool> setResourceTags(String resourceId, List<String> tags) async {
    try {
      await Net.getRestClient().setResourceTags({
        'resourceId': resourceId,
        'tags': tags,
      });
      refreshDatasource();
      return true;
    } catch (e) {
      e.toString().toast;
      return false;
    }
  }

  // 添加资源标签
  Future<bool> addResourceTag(String resourceId, String tag) async {
    try {
      await Net.getRestClient().addResourceTag({
        'resourceId': resourceId,
        'tag': tag,
      });
      refreshDatasource();
      return true;
    } catch (e) {
      e.toString().toast;
      return false;
    }
  }

  // 移除资源标签
  Future<bool> removeResourceTag(String resourceId, String tag) async {
    try {
      await Net.getRestClient().removeResourceTag(resourceId, tag);
      refreshDatasource();
      return true;
    } catch (e) {
      e.toString().toast;
      return false;
    }
  }

  // 根据标签获取资源
  Future<List<ResourceResp>> getResourcesByTags(List<String> tags, {String langCode = ''}) async {
    try {
      var result = await Net.getRestClient().getResourcesByTags(tags, langCode);
      return result.data;
    } catch (e) {
      e.toString().toast;
      return [];
    }
  }
}
