import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/mother_tongue_resp/mother_tongue_resp.dart';
import 'package:lsenglish_admin/models/ted_source_resp/language.dart';
import 'package:lsenglish_admin/models/ted_source_resp/subtitle.dart';
import 'package:lsenglish_admin/models/ted_source_resp/ted_source_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/file.dart';
import 'package:lsenglish_admin/util/oss.dart';
import 'package:lsenglish_admin/util/ted.dart';
import 'package:lsenglish_admin/util/time.dart';
import 'package:lsenglish_admin/util/url.dart';
import 'package:lsenglish_admin/widgets/ted_webview.dart';

import '../controllers/resource_controller.dart';
import 'resource_add_widget.dart';

class ResourceAddFromTedWidget extends StatefulWidget {
  const ResourceAddFromTedWidget({super.key});

  @override
  State<ResourceAddFromTedWidget> createState() => _CategoryAddWidgetState();
}

class _CategoryAddWidgetState extends State<ResourceAddFromTedWidget> {
  final TextEditingController _controllerLink = TextEditingController();
  var parsing = false;
  var parseText = "";
  var manualVideoLink = "";
  TedSourceResp? tedSourceResp;
  List<CategoryResp> categories = [];
  List<String> selectCategoryIds = [];
  @override
  void initState() {
    super.initState();
    _controllerLink.text = "https://www.ted.com/talks/wan_faridah_akmal_jusoh_the_luminous_mystery_of_fireflies?";
    Net.getRestClient().categoryList(1, 100).then((onValue) {
      categories.clear();
      setState(() {
        categories.addAll(onValue.data.data);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("添加TED资源"),
      content: SizedBox(
        width: 700,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
          Text('选择分类', style: Get.textTheme.titleMedium),
          const Gap(20),
          Wrap(
            spacing: 8.0, // horizontal space between chips
            runSpacing: 4.0, // vertical space between lines
            children: categories
                .map((category) => GestureDetector(
                      onTap: () {
                        setState(() {
                          if (selectCategoryIds.contains(category.id)) {
                            selectCategoryIds.remove(category.id ?? "");
                          } else {
                            selectCategoryIds.add(category.id ?? "");
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                        decoration: BoxDecoration(
                            color: selectCategoryIds.contains(category.id) ? Get.theme.primaryColor : Colors.grey,
                            borderRadius: BorderRadius.circular(16)),
                        child: Text(
                          category.name ?? "",
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ))
                .toList(),
          ),
          const Gap(20),
          TextField(
            controller: _controllerLink,
            decoration: const InputDecoration(
              labelText: '复制Ted视频链接',
              filled: true,
            ),
          ),
          const Gap(20),
          FilledButton(
            onPressed: () {
              Get.dialog(TedWebviewWidget(
                tedUrl: _controllerLink.text,
                tedWebviewCallback: (String downloadUrl) {
                  setState(() {
                    manualVideoLink = downloadUrl;
                  });
                },
              ));
            },
            child: const Text("手动下载ted视频(如果视频解析下载失败)"),
          ),
          Visibility(visible: manualVideoLink.isNotEmpty, child: Text("\n已解析出视频地址$manualVideoLink,再次点击解析")),
          const Gap(20),
          Visibility(visible: parsing, child: const CircularProgressIndicator()),
          const Gap(20),
          Visibility(
              visible: parseText != "", child: ConstrainedBox(constraints: const BoxConstraints(maxHeight: 300), child: SelectableText(parseText))),
          const Gap(20),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("解析并添加"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (selectCategoryIds.isEmpty) {
      Get.snackbar("错误", "请选择至少一个资源分类");
      return false;
    }
    if (_controllerLink.text.isEmpty) {
      Get.snackbar("错误", "请填写链接");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    setState(() {
      parsing = true;
      parseText = "开始解析链接...\n";
    });
    try {
      if (tedSourceResp == null) {
        var result = await fetchTedVideo(removeLastQuestionMark(_controllerLink.text));
        if (result == null) {
          setState(() {
            parseText += "解析出错...\n";
          });
          return;
        } else {
          tedSourceResp = result;
        }
      }
    } catch (e) {
      setState(() {
        parsing = false;
        parseText += "解析出错 $e...\n";
      });
      return;
    }
    setState(() {
      parseText += "正在下载视频...\n";
    });
    String? videoSavePath;
    var videoFileName = "tempVideoDownload.mp4";
    try {
      videoSavePath = await downloadLargeFile(
        manualVideoLink.isNotEmpty ? manualVideoLink : tedSourceResp!.playerUrl!,
        videoFileName,
        onProgress: (received, total, speed) {
          String progressUpdate(int received, int total, double speed) {
            double receivedMB = received / 1048576; // 将接收的字节转换为 MB
            double totalMB = total / 1048576; // 将总字节转换为 MB
            double speedMBs = speed / 1048576; // 将速度从字节/秒转换为 MB/秒

            return "正在下载视频(进度: ${(received / total * 100).toStringAsFixed(2)}% "
                "已下载: ${receivedMB.toStringAsFixed(2)} of ${totalMB.toStringAsFixed(2)} MB "
                "网速: ${speedMBs.toStringAsFixed(2)} MB/s)...\n";
          }

          setState(() {
            parseText = progressUpdate(received, total, speed);
          });
        },
      );
      setState(() {
        parseText = "视频下载已完成";
      });
    } catch (e) {
      setState(() {
        parsing = false;
        parseText += "下载视频出错 $e...\n";
      });
      return;
    }
    setState(() {
      parseText += "已下载视频到$videoSavePath\n";
      parseText += "正在下载图片...\n";
    });

    String? imageSavePath;
    var imageFileName = generateFormattedFilename(getFileExtensionFromUrl(tedSourceResp!.thumb!));
    try {
      imageSavePath = await downloadFile(tedSourceResp!.thumb!, imageFileName);
      parseText += "已下载图片到$imageSavePath\n";
    } catch (e) {
      setState(() {
        parsing = false;
        parseText += "下载视频封面出错 $e...\n";
      });
      return;
    }
    if (imageSavePath == null) {
      setState(() {
        parsing = false;
        parseText += "下载视频封面出错 ...\n";
      });
      return;
    }
    setState(() {
      parseText += "正在上传服务器...\n";
    });
    var videoRemoteUrl = await OssUtil().uploadVideoFile(filePath: videoSavePath) ?? "";
    var imageRemoteUrl = await OssUtil().uploadSystemResourceImageFile(filePath: imageSavePath) ?? "";
    setState(() {
      parseText += "正在清理文件...\n";
    });
    deleteFile(imageSavePath);
    deleteFile(videoSavePath);
    setState(() {
      parseText += "清理成功...\n";
    });
    var nativeLangListResult = await Net.getRestClient().nativeLangList();
    var resourceRelationReq = await getTitlesAndDescs(nativeLangListResult.data, tedSourceResp!.languages, tedSourceResp!.subtitles, _controllerLink.text);
    setState(() {
      parseText += "开始添加...\n";
    });
    ResourceAddReq tempResourceResp = ResourceAddReq(
      originResourceRelation: resourceRelationReq.where((e) => e.langCode == "en").first,
      resourceRelations: resourceRelationReq.where((e) => e.langCode != "en").toList(),
      cover: imageRemoteUrl,
      videoUrl: videoRemoteUrl,
      categoryIds: selectCategoryIds,
      duration: tedSourceResp!.duration,
      publishedAt: tedSourceResp!.publishedAt,
      author: tedSourceResp!.presenterDisplayName,
    );
    try {
      await Get.find<ResourceController>().addEntity(tempResourceResp.toMap());
      Get.back();
      Get.snackbar("成功", "添加成功");
    } catch (e) {
      setState(() {
        parseText += "添加失败($e)\n";
      });
      OssUtil().deleteOssFile(imageRemoteUrl);
      OssUtil().deleteOssFile(videoRemoteUrl);
      Get.snackbar("错误", e.toString());
    }
  }

// https://www.ted.com/talks/hiroki_koga_the_sweet_future_of_vertical_farming/transcript.json?language=en
// 使用这种来获取字幕
// https://www.ted.com/services/v1/oembed.json?url=https%3A%2F%2Fwww.ted.com%2Ftalks%2Frebecca_mcmackin_let_your_garden_grow_wild
  Future<List<ResourceRelationReq>> getTitlesAndDescs(
      List<NativeLangResp> nativeLangRespList, List<TedSourceLanguage>? languages, List<TedSourceSubtitle>? subtitles, String controllerLinkText) async {
    if (languages == null || languages.isEmpty) {
      return [];
    }
    var resourceRelations = <ResourceRelationReq>[];
    var serverLangList = nativeLangRespList.map((e) => e.code);
    for (var i = 0; i < languages.length; i++) {
      if (serverLangList.contains(languages[i].languageCode)) {
        var nativeLangResp = nativeLangRespList.firstWhere((element) => element.code == languages[i].languageCode);
        try {
          var subtitle = subtitles?.firstWhere((element) => element.code == languages[i].languageCode);
          // var subtitleName = generateFormattedFilename(getFileExtensionFromUrl(subtitle.webvtt!));
          // setState(() {
          //   parseText = "正在获取字幕文件${subtitle.name}的数据...";
          // });
          // String? subtitlePath;
          // try {
          //   subtitlePath = await downloadFile(subtitle.webvtt!, subtitleName);
          // } catch (e) {
          //   setState(() {
          //     parsing = false;
          //     parseText = "下载字幕文件出错 $e...";
          //   });
          //   return [];
          // }
          // var multipartFile = await dio.MultipartFile.fromFile(subtitlePath!, filename: subtitleName);
          // var result = await Net.getRestClient().uploadFiles(files: [multipartFile]);
          // subtitleReqList.add(ResourceLangKVReq(nativeLangId: nativeLangResp.id, content: result.data.first));
          // deleteFile(subtitlePath);
          var langUrl =
              "https://www.ted.com/services/v1/oembed.json?url=${removeLastQuestionMark(controllerLinkText)}?language=${nativeLangResp.code!}";
          debugPrint("langUrl=$langUrl");
          var result = await fetechTitleAndDesc(langUrl);
          debugPrint("fetechTitleAndDesc=$result");
          resourceRelations.add(ResourceRelationReq(
            langCode: nativeLangResp.code,
            title: result.first,
            description: result.second,
            subtitleUrl: subtitle?.webvtt,
          ));
        } catch (e) {
          debugPrint("解析出错: $e");
          return [];
        }
      }
    }
    return resourceRelations;
  }
}
