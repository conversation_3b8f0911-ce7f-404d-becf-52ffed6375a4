import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/resource/views/resource_add_from_ted_widget.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/resource_resp/resource_resp.dart';
import 'package:lsenglish_admin/util/image.dart';
import 'resource_tag_widget.dart';

import '../controllers/resource_controller.dart';

class ResourceView extends BasePageListView<ResourceResp, ResourceController> {
  const ResourceView({Key? key}) : super(key: key);

  @override
  List<Widget> customActions() {
    return [
      FilledButton(
        onPressed: () {
          Get.dialog(const ResourceAddFromTedWidget(), barrierDismissible: false);
        },
        child: const Text("从Ted链接解析"),
      ),
      const Gap(20),
      Obx(() => DropdownButton<CategoryResp>(
            value: controller.categorySelect.value,
            icon: const Icon(Icons.arrow_downward),
            elevation: 16,
            focusColor: Colors.transparent,
            onChanged: (CategoryResp? value) {
              controller.categorySelect.value = value ?? CategoryResp();
              controller.refreshDatasource();
            },
            items: controller.categoryList.map<DropdownMenuItem<CategoryResp>>((CategoryResp value) {
              return DropdownMenuItem<CategoryResp>(
                value: value,
                child: Text(value.name ?? ""),
              );
            }).toList(),
          )),
      const Gap(20),
    ];
  }

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID")),
      DataColumn2(label: Text("优先级"), fixedWidth: 100),
      DataColumn2(label: Text("默认标题")),
      DataColumn2(label: Text("视频地址")),
      DataColumn2(label: Text("封面地址")),
      DataColumn2(label: Text("标签")),
      DataColumn2(label: Text("是否精选")),
      DataColumn2(label: Text("操作"), fixedWidth: 100),
    ];
  }

  @override
  String getTitle() {
    return "资源管理";
  }

  @override
  void onAddPressed() {
    controller.addNewOne();
  }

  @override
  List<DataCell> buildDataCells(ResourceResp item, int index) {
    return [
      DataCell(Text(item.id.toString())),
      DataCell(FilledButton(
        onPressed: () {
          controller.setPriority(item);
        },
        child: Text(item.priority.toString()),
      )),
      DataCell(Text(item.defaultLangTitle ?? "--")),
      DataCell(Text(item.videoUrl ?? "--")),
      DataCell(ImageLoader(item.cover ?? "", size: 100)),
      DataCell(
        item.tags != null && item.tags!.isNotEmpty
            ? Wrap(
                spacing: 4,
                runSpacing: 4,
                children: item.tags!.map((tag) {
                  return Chip(
                    label: Text(tag, style: const TextStyle(fontSize: 12)),
                    visualDensity: VisualDensity.compact,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              )
            : FilledButton(
                onPressed: () {
                  _showTagManagementDialog(item);
                },
                child: const Text("添加标签"),
              ),
      ),
      DataCell(
        FilledButton(
          onPressed: () {
            controller.setFeaturedContent(item);
          },
          style: item.isFeaturedContent == true
              ? ButtonStyle(
                  backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                  foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                )
              : null,
          child: Text(item.isFeaturedContent == true ? "取消精选" : "设置精选"),
        ),
      ),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_modify.tr)),
              onPressed: () {
                controller.updateSelect(item, index);
              },
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.label_outline),
              child: const Padding(padding: EdgeInsets.only(right: defaultPadding * 2), child: Text("管理标签")),
              onPressed: () {
                _showTagManagementDialog(item);
              },
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.delete_outline),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_delete.tr)),
              onPressed: () {
                controller.deleteByIndex(item, index);
              },
            )
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId(ResourceResp item, int index) {
    return item.id;
  }

  @override
  double? getSpecificRowHeight() {
    return 100;
  }

  void _showTagManagementDialog(ResourceResp item) {
    Get.dialog(
      ResourceTagWidget(
        resource: item,
        onTagsUpdated: () {
          controller.refreshDatasource();
        },
      ),
      barrierDismissible: true,
    );
  }
}
