import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/resource/controllers/resource_controller.dart';
import 'package:lsenglish_admin/models/resource_resp/resource_resp.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/dialog_wrap.dart';

class ResourceTagWidget extends StatefulWidget {
  final ResourceResp resource;
  final Function()? onTagsUpdated;

  const ResourceTagWidget({
    Key? key,
    required this.resource,
    this.onTagsUpdated,
  }) : super(key: key);

  @override
  State<ResourceTagWidget> createState() => _ResourceTagWidgetState();
}

class _ResourceTagWidgetState extends State<ResourceTagWidget> {
  final ResourceController _controller = Get.find<ResourceController>();
  final TextEditingController _tagController = TextEditingController();
  List<String> _tags = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTags();
  }

  Future<void> _loadTags() async {
    setState(() {
      _isLoading = true;
    });

    final tags = await _controller.getResourceTags(widget.resource.id ?? "");
    
    setState(() {
      _tags = tags;
      _isLoading = false;
    });
  }

  Future<void> _addTag() async {
    final tag = _tagController.text.trim();
    if (tag.isEmpty) {
      "标签不能为空".toast;
      return;
    }

    if (_tags.contains(tag)) {
      "标签已存在".toast;
      return;
    }

    final success = await _controller.addResourceTag(widget.resource.id ?? "", tag);
    if (success) {
      _tagController.clear();
      await _loadTags();
      if (widget.onTagsUpdated != null) {
        widget.onTagsUpdated!();
      }
    }
  }

  Future<void> _removeTag(String tag) async {
    final success = await _controller.removeResourceTag(widget.resource.id ?? "", tag);
    if (success) {
      await _loadTags();
      if (widget.onTagsUpdated != null) {
        widget.onTagsUpdated!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DialogWrapWidget(
      maxWidth: Get.width * 0.4,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "标签管理",
            style: Get.textTheme.titleLarge,
          ),
          const Gap(20),
          Text("资源: ${widget.resource.defaultLangTitle ?? ''}"),
          const Gap(20),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _tagController,
                  decoration: const InputDecoration(
                    labelText: '输入标签',
                    hintText: '输入标签后点击添加',
                    filled: true,
                  ),
                  onSubmitted: (_) => _addTag(),
                ),
              ),
              const Gap(10),
              FilledButton(
                onPressed: _addTag,
                child: const Text("添加标签"),
              ),
            ],
          ),
          const Gap(20),
          Text(
            "当前标签:",
            style: Get.textTheme.titleMedium,
          ),
          const Gap(10),
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _tags.isEmpty
                  ? const Text("暂无标签")
                  : Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _tags.map((tag) {
                        return Chip(
                          label: Text(tag),
                          deleteIcon: const Icon(Icons.close, size: 18),
                          onDeleted: () => _removeTag(tag),
                        );
                      }).toList(),
                    ),
          const Gap(20),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              FilledButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text("关闭"),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tagController.dispose();
    super.dispose();
  }
}
