import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/models/user_resp.dart';

import '../../../../common/constants.dart';
import '../../../../generated/locales.g.dart';
import '../controllers/users_controller.dart';

class UsersView extends BasePageListView<UserResp, UsersController> {
  const UsersView({Key? key}) : super(key: key);

  @override
  List<DataColumn> getDataColumns() {
    return [
      DataColumn2(label: Text(LocaleKeys.user_name.tr)),
      DataColumn2(label: Text(LocaleKeys.user_role.tr)),
      DataColumn2(label: Text(LocaleKeys.user_status.tr)),
      DataColumn2(label: Text(LocaleKeys.user_create_time.tr)),
    ];
  }

  @override
  String getTitle() {
    return LocaleKeys.drawer_user_manager.tr;
  }

  @override
  void onAddPressed() {}

  @override
  List<DataCell> buildDataCells(UserResp item, int index) {
    return [
      DataCell(Text(item.nickname ?? "--")),
      DataCell(Text(item.roleName ?? "--")),
      DataCell(Switch(value: true, onChanged: (bool onChanged) {})),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_modify.tr)),
              onPressed: () {},
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.delete_outline),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_delete.tr)),
              onPressed: () {},
            )
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId(UserResp item, int index) {
    return item.id;
  }
}
