import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/user_resp.dart';
import 'package:lsenglish_admin/net/net.dart';

class UsersController extends PageListController<UserResp> {
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void deleteByIndex(UserResp item, int index) {}

  @override
  void updateSelect(UserResp item, int index) {}

  @override
  Future<void> addEntity(Map<String, dynamic> data) async{}

  @override
  Future<PageListResp<List<UserResp>>> getPageList(
      int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().userList(page, size);
    return result.data;
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient()
        .deleteUserMulti(ids)
        .then((value) => refreshDatasource());
  }
}
