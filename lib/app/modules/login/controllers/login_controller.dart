import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginController extends GetxController {
  final TextEditingController controllerUsername = TextEditingController();
  final TextEditingController controllerPassword = TextEditingController();
  var userNameFocusNode = FocusNode();
  var passwordFocusNode = FocusNode();
  final count = 0.obs;
  final showUserTextfileError = false.obs;
  final showPasswordTextfileError = false.obs;
  final showPasswordText = false.obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    controllerUsername.addListener(() {
      final text = controllerUsername.text;
      if (text.isNotEmpty) {
        showUserTextfileError.value = false;
      }
    });
    userNameFocusNode.addListener(() {
      if (!userNameFocusNode.hasFocus) {
        showUserTextfileError.value =
            controllerUsername.text.isEmpty ? true : false;
      }
    });

    controllerPassword.addListener(() {
      final text = controllerPassword.text;
      if (text.isNotEmpty) {
        showPasswordTextfileError.value = false;
      }
    });
    passwordFocusNode.addListener(() {
      if (!passwordFocusNode.hasFocus) {
        showPasswordTextfileError.value =
            controllerPassword.text.isEmpty ? true : false;
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    // controllerOutlined.dispose();
  }

  void increment() => count.value++;
}
