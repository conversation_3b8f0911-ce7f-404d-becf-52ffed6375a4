import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/app/routes/app_pages.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/util/responsive.dart';

import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Card(
          child: SizedBox(
            width:
                Responsive.isMobile(context) ? Get.width / 1.3 : Get.width / 3,
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding * 2),
              child: IntrinsicHeight(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      "Welcome back",
                      style: Get.textTheme.headlineLarge,
                    ),
                    const SizedBox(height: defaultPadding * 2),
                    Obx(
                      () => SizedBox(
                        height: 80,
                        child: TextField(
                          focusNode: controller.userNameFocusNode,
                          controller: controller.controllerUsername,
                          decoration: InputDecoration(
                            labelText: LocaleKeys.input_user_name_label.tr,
                            errorText: controller.showUserTextfileError.value
                                ? LocaleKeys.input_user_name_error.tr
                                : null,
                            border: const OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12.0))),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    Obx(
                      () => SizedBox(
                        height: 80,
                        child: TextField(
                          obscureText: !controller.showPasswordText.value,
                          focusNode: controller.passwordFocusNode,
                          controller: controller.controllerPassword,
                          decoration: InputDecoration(
                            suffixIcon: Padding(
                              padding: const EdgeInsetsDirectional.only(
                                  end: defaultPadding / 2),
                              child: IconButton(
                                icon: Icon(controller.showPasswordText.value
                                    ? Icons.visibility
                                    : Icons.visibility_off),
                                onPressed: () => controller.showPasswordText
                                    .value = !controller.showPasswordText.value,
                              ),
                            ),
                            labelText: LocaleKeys.input_password_label.tr,
                            errorText:
                                controller.showPasswordTextfileError.value
                                    ? LocaleKeys.input_password_error.tr
                                    : null,
                            border: const OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12.0))),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: FilledButton(
                          onPressed: () {
                            Get.offAllNamed(Routes.HOME);
                          },
                          style: ElevatedButton.styleFrom(
                            elevation: 8,
                          ),
                          child: Text(LocaleKeys.common_login.tr),
                        ))
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
