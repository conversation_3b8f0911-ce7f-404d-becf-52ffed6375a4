import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/category_type/views/category_type_add_widget.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/category_type_resp/category_type_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';

class CategoryTypeController extends PageListController<CategoryTypeResp> {
  @override
  Future<PageListResp<List<CategoryTypeResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().categoryTypeList(page, size, sortType: sortSelect.value);
    return result.data;
  }

  @override
  void updateSelect(CategoryTypeResp item, int index) async {
    await Get.dialog(CategoryTypeAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex(CategoryTypeResp item, int index) async {
    await Net.getRestClient().deleteCategoryType(item.id ?? "");
    refreshDatasource();
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    Net.getRestClient().addCategoryType(data).then((value) => refreshDatasource());
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().deleteCategoryTypeMulti(ids).then((value) => refreshDatasource());
  }

  @override
  String? getDeleteDialogTitle() {
    return "删除分类类型会将所有该类型下的分类删除\n是否确定删除?";
  }

  void setPriority(CategoryTypeResp item) {
    CommonInputDialog(
      title: "修改优先级(越大优先级越高)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.priority.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          await Net.getRestClient()
              .setCategoryTypePriority({'id': item.id, 'priority': int.parse(content)})
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }
}
