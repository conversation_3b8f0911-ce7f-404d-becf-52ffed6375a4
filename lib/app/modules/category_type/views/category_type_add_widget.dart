import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/category_type_resp/category_type_resp.dart';
import '../controllers/category_type_controller.dart';

class CategoryTypeAddWidget extends StatefulWidget {
  final CategoryTypeResp? resp;
  const CategoryTypeAddWidget({super.key, this.resp});

  @override
  State<CategoryTypeAddWidget> createState() => _CategoryTypeAddWidgetState();
}

class _CategoryTypeAddWidgetState extends State<CategoryTypeAddWidget> {
  final TextEditingController _controllerName = TextEditingController();
  final TextEditingController _controllerDesc = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controllerName.text = widget.resp?.name ?? "";
    _controllerDesc.text = widget.resp?.description ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Card(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.resp?.id == null ? "添加分类类型" : "编辑分类类型",
                  style: Get.textTheme.titleLarge,
                ),
                const Gap(20),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controllerName,
                    decoration: const InputDecoration(
                      labelText: '分类类型名称',
                      filled: true,
                    ),
                  ),
                ),
                const Gap(20),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controllerDesc,
                    decoration: const InputDecoration(
                      labelText: '分类类型描述',
                      filled: true,
                    ),
                  ),
                ),
                const Gap(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FilledButton(
                      onPressed: () {
                        _add();
                        Get.back();
                      },
                      child: Text(widget.resp?.id == null ? "添加" : "更新"),
                    ),
                    const Gap(10),
                    FilledButton(
                      onPressed: () {
                        Get.back();
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                        foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                      ),
                      child: const Text("取消"),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _add() {
    CategoryTypeResp tempCategoryTypeResp;
    if (widget.resp == null) {
      tempCategoryTypeResp = CategoryTypeResp();
    } else {
      tempCategoryTypeResp = widget.resp!;
    }
    tempCategoryTypeResp.name = _controllerName.text;
    tempCategoryTypeResp.description = _controllerDesc.text;
    Get.find<CategoryTypeController>().addEntity(tempCategoryTypeResp.toJson());
  }

  @override
  void dispose() {
    _controllerName.dispose();
    _controllerDesc.dispose();
    super.dispose();
  }
}
