import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/category_type_resp/category_type_resp.dart';
import '../controllers/category_type_controller.dart';

class CategoryTypeAddWidget extends StatefulWidget {
  final CategoryTypeResp? resp;
  const CategoryTypeAddWidget({super.key, this.resp});

  @override
  State<CategoryTypeAddWidget> createState() => _CategoryTypeAddWidgetState();
}

class _CategoryTypeAddWidgetState extends State<CategoryTypeAddWidget> {
  final TextEditingController _controllerName = TextEditingController();
  final TextEditingController _controllerDesc = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controllerName.text = widget.resp?.name ?? "";
    _controllerDesc.text = widget.resp?.description ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp?.id == null ? "添加分类类型" : "编辑分类类型"),
      content: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _controllerName,
                decoration: const InputDecoration(
                  labelText: '分类类型名称 *',
                  border: OutlineInputBorder(),
                ),
              ),
              const Gap(16),
              TextField(
                controller: _controllerDesc,
                decoration: const InputDecoration(
                  labelText: '分类类型描述',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (_controllerName.text.isEmpty) {
      Get.snackbar("错误", "分类类型名称不能为空");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    try {
      CategoryTypeResp tempCategoryTypeResp;
      if (widget.resp == null) {
        tempCategoryTypeResp = CategoryTypeResp();
      } else {
        tempCategoryTypeResp = widget.resp!;
      }
      tempCategoryTypeResp.name = _controllerName.text;
      tempCategoryTypeResp.description = _controllerDesc.text;

      await Get.find<CategoryTypeController>().addEntity(tempCategoryTypeResp.toJson());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    _controllerName.dispose();
    _controllerDesc.dispose();
    super.dispose();
  }
}
