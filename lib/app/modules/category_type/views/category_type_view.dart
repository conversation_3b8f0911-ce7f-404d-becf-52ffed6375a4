import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/models/category_type_resp/category_type_resp.dart';

import '../controllers/category_type_controller.dart';
import 'category_type_add_widget.dart';

class CategoryTypeView extends BasePageListView<CategoryTypeResp, CategoryTypeController> {
  const CategoryTypeView({Key? key}) : super(key: key);

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID")),
      DataColumn2(label: Text("优先级"), fixedWidth: 100),
      DataColumn2(label: Text("名称")),
      DataColumn2(label: Text("描述")),
      DataColumn2(label: Text("操作"), fixedWidth: 100),
    ];
  }

  @override
  void onAddPressed() async {
    await Get.dialog(const CategoryTypeAddWidget(), barrierDismissible: false);
  }

  @override
  String getTitle() {
    return "分类类型管理";
  }

  @override
  List<DataCell> buildDataCells(CategoryTypeResp item, int index) {
    return [
      DataCell(Text(item.id.toString())),
      DataCell(FilledButton(
        onPressed: () {
          controller.setPriority(item);
        },
        child: Text(item.priority.toString()),
      )),
      DataCell(Text(item.name ?? "--")),
      DataCell(Text(item.description ?? "--")),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_modify.tr)),
              onPressed: () {
                controller.updateSelect(item, index);
              },
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.delete_outline),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_delete.tr)),
              onPressed: () {
                controller.deleteByIndex(item, index);
              },
            )
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId(CategoryTypeResp item, int index) {
    return item.id;
  }
}
