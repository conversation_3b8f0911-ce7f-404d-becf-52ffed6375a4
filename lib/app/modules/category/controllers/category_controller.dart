import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/category/views/category_add_widget.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/dialog/delete_dialog.dart';
import 'package:lsenglish_admin/dialog/dialog_extension.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';

class CategoryController extends PageListController<CategoryResp> {
  @override
  Future<PageListResp<List<CategoryResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().categoryList(page, size, sortType: sortSelect.value);
    return result.data;
  }

  @override
  void updateSelect(CategoryResp item, int index) async {
    await Get.dialog(CategoryAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex(CategoryResp item, int index) async {
    DeleteDialog(
      title: getDeleteDialogTitle(),
      onPressed: () async {
        await Net.getRestClient().deleteCategory(item.id ?? "");
        refreshDatasource();
      },
    ).dialog();
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    await Net.getRestClient().addCategory(data);
    refreshDatasource();
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().deleteCategoryMulti(ids).then((value) => refreshDatasource());
  }

  @override
  String? getDeleteDialogTitle() {
    return "删除分类会将所有分类下的子类删除\n是否确定删除?";
  }

  void setPriority(CategoryResp item) {
    CommonInputDialog(
      title: "修改优先级(越大优先级越高)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.priority.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          await Net.getRestClient()
              .setCategoryPriority({'id': item.id, 'priority': int.parse(content)})
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }
}
