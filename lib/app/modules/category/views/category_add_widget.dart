import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import '../controllers/category_controller.dart';

class CategoryAddWidget extends StatefulWidget {
  final CategoryResp? resp;
  const CategoryAddWidget({super.key, this.resp});

  @override
  State<CategoryAddWidget> createState() => _CategoryAddWidgetState();
}

class _CategoryAddWidgetState extends State<CategoryAddWidget> {
  final TextEditingController _controllerName = TextEditingController();
  final TextEditingController _controllerDesc = TextEditingController();
  List<String> categoryTypes = ["等级", "场景", "主题"];
  List<String> categoryTypesIds = ["1", "2", "3"];
  var selectCategoryTypesIndex = 0;

  @override
  void initState() {
    super.initState();
    _controllerName.text = widget.resp?.name ?? "";
    _controllerDesc.text = widget.resp?.description ?? "";
    selectCategoryTypesIndex = categoryTypesIds.indexOf(widget.resp?.categoryTypeId ?? "1");
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加分类" : "编辑分类"),
      content: SizedBox(
        width: 500,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('选择分类的类型', style: Get.textTheme.titleMedium),
              const Gap(16),
              Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children: categoryTypes.asMap().entries.map((entry) {
                  int index = entry.key;
                  String types = entry.value;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectCategoryTypesIndex = index;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                      decoration: BoxDecoration(
                          color: selectCategoryTypesIndex == index ? Get.theme.primaryColor : Colors.grey,
                          borderRadius: BorderRadius.circular(16)),
                      child: Text(
                        types,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  );
                }).toList(),
              ),
              const Gap(16),
              TextField(
                controller: _controllerName,
                decoration: const InputDecoration(
                  labelText: '分类名称 *',
                  border: OutlineInputBorder(),
                ),
              ),
              const Gap(16),
              TextField(
                controller: _controllerDesc,
                decoration: const InputDecoration(
                  labelText: '分类描述',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (_controllerName.text.isEmpty) {
      Get.snackbar("错误", "分类名称不能为空");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    CategoryResp tempResourceResp;
    if (widget.resp == null) {
      tempResourceResp = CategoryResp();
    } else {
      tempResourceResp = widget.resp!;
    }
    tempResourceResp.name = _controllerName.text;
    tempResourceResp.description = _controllerDesc.text;
    tempResourceResp.categoryTypeId = categoryTypesIds[selectCategoryTypesIndex];

    try {
      await Get.find<CategoryController>().addEntity(tempResourceResp.toJson());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }
}
