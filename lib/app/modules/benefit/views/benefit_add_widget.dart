import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';
import '../controllers/benefit_controller.dart';

class BenefitAddWidget extends StatefulWidget {
  final BenefitResp? resp;
  const BenefitAddWidget({super.key, this.resp});

  @override
  State<BenefitAddWidget> createState() => _BenefitAddWidgetState();
}

class _BenefitAddWidgetState extends State<BenefitAddWidget> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController levelController = TextEditingController();
  final TextEditingController cycleCountController = TextEditingController();
  final TextEditingController benefitCountController = TextEditingController();
  final TextEditingController sortController = TextEditingController();
  final TextEditingController benefitGroupIdController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  
  int selectedCycleType = 1; // 默认为日
  int selectedStatus = 1; // 默认为启用

  // 周期类型选项
  final List<Map<String, dynamic>> cycleTypeOptions = [
    {'value': 1, 'label': '日'},
    {'value': 2, 'label': '周'},
    {'value': 3, 'label': '月'},
    {'value': 4, 'label': '季'},
    {'value': 5, 'label': '年'},
    {'value': 6, 'label': '无周期'},
  ];

  @override
  void initState() {
    super.initState();
    if (widget.resp != null) {
      nameController.text = widget.resp!.name ?? "";
      codeController.text = widget.resp!.code ?? "";
      levelController.text = widget.resp!.level?.toString() ?? "";
      cycleCountController.text = widget.resp!.cycleCount?.toString() ?? "";
      benefitCountController.text = widget.resp!.benefitCount?.toString() ?? "";
      sortController.text = widget.resp!.sort?.toString() ?? "";
      benefitGroupIdController.text = widget.resp!.benefitGroupId?.toString() ?? "";
      descriptionController.text = widget.resp!.description ?? "";
      selectedCycleType = widget.resp!.cycleType ?? 1;
      selectedStatus = widget.resp!.status ?? 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加权益" : "编辑权益"),
      content: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: "权益名称 *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: codeController,
                      decoration: const InputDecoration(
                        labelText: "权益编码 *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: levelController,
                      decoration: const InputDecoration(
                        labelText: "权益等级 *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: "周期类型 *",
                        border: OutlineInputBorder(),
                      ),
                      value: selectedCycleType,
                      items: cycleTypeOptions.map((option) {
                        return DropdownMenuItem<int>(
                          value: option['value'],
                          child: Text(option['label']),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedCycleType = value ?? 1;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: cycleCountController,
                      decoration: const InputDecoration(
                        labelText: "周期数量 *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: benefitCountController,
                      decoration: const InputDecoration(
                        labelText: "权益数量 *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: sortController,
                      decoration: const InputDecoration(
                        labelText: "排序 *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: "状态 *",
                        border: OutlineInputBorder(),
                      ),
                      value: selectedStatus,
                      items: const [
                        DropdownMenuItem(value: 0, child: Text("禁用")),
                        DropdownMenuItem(value: 1, child: Text("启用")),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedStatus = value ?? 1;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const Gap(16),
              TextField(
                controller: benefitGroupIdController,
                decoration: const InputDecoration(
                  labelText: "权益组ID",
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const Gap(16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: "描述",
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (nameController.text.isEmpty) {
      Get.snackbar("错误", "权益名称不能为空");
      return false;
    }
    if (codeController.text.isEmpty) {
      Get.snackbar("错误", "权益编码不能为空");
      return false;
    }
    if (levelController.text.isEmpty) {
      Get.snackbar("错误", "权益等级不能为空");
      return false;
    }
    if (cycleCountController.text.isEmpty) {
      Get.snackbar("错误", "周期数量不能为空");
      return false;
    }
    if (benefitCountController.text.isEmpty) {
      Get.snackbar("错误", "权益数量不能为空");
      return false;
    }
    if (sortController.text.isEmpty) {
      Get.snackbar("错误", "排序不能为空");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    Map<String, dynamic> data = {
      'name': nameController.text,
      'code': codeController.text,
      'level': int.parse(levelController.text),
      'cycleType': selectedCycleType,
      'cycleCount': int.parse(cycleCountController.text),
      'benefitCount': int.parse(benefitCountController.text),
      'sort': int.parse(sortController.text),
      'status': selectedStatus,
      'description': descriptionController.text,
    };

    if (benefitGroupIdController.text.isNotEmpty) {
      data['benefitGroupId'] = int.parse(benefitGroupIdController.text);
    }

    if (widget.resp != null) {
      data['id'] = widget.resp!.id;
    }

    try {
      await Get.find<BenefitController>().addEntity(data);
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    codeController.dispose();
    levelController.dispose();
    cycleCountController.dispose();
    benefitCountController.dispose();
    sortController.dispose();
    benefitGroupIdController.dispose();
    descriptionController.dispose();
    super.dispose();
  }
}
