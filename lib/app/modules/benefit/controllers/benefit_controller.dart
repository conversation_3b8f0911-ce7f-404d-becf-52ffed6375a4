import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';
import '../views/benefit_add_widget.dart';

class BenefitController extends PageListController<BenefitResp> {
  // 搜索条件
  var nameFilter = ''.obs;
  var benefitGroupCodeFilter = ''.obs;
  var statusFilter = (-1).obs; // -1表示全部，0禁用，1启用

  @override
  Future<PageListResp<List<BenefitResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    
    // 构建查询参数
    String? name = nameFilter.value.isEmpty ? null : nameFilter.value;
    String? benefitGroupCode = benefitGroupCodeFilter.value.isEmpty ? null : benefitGroupCodeFilter.value;
    int? status = statusFilter.value == -1 ? null : statusFilter.value;
    
    var result = await Net.getRestClient().benefitList(
      page, 
      size,
      name: name,
      benefitGroupCode: benefitGroupCode,
      status: status,
    );
    return result.data;
  }

  @override
  void updateSelect(BenefitResp item, int index) async {
    await Get.dialog(BenefitAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex(BenefitResp item, int index) async {
    // 权益不允许删除
    Get.snackbar("提示", "权益不允许删除");
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    if (data.containsKey('id') && data['id'] != null) {
      // 更新操作
      await Net.getRestClient().updateBenefit(data);
    } else {
      // 添加操作
      await Net.getRestClient().addBenefit(data);
    }
    refreshDatasource();
  }

  @override
  void deleteMulti(List<String> ids) {
    // 权益不允许批量删除
    Get.snackbar("提示", "权益不允许删除");
  }

  // 设置排序的方法
  void setSort(BenefitResp item) {
    CommonInputDialog(
      title: "修改排序(越大越靠前)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.sort.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          Map<String, dynamic> data = {
            'id': item.id,
            'sort': int.parse(content),
          };
          await Net.getRestClient()
              .updateBenefit(data)
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }

  // 切换状态
  void toggleStatus(BenefitResp item) async {
    int newStatus = item.status == 1 ? 0 : 1;
    Map<String, dynamic> data = {
      'id': item.id,
      'status': newStatus,
    };
    
    try {
      await Net.getRestClient().updateBenefit(data);
      refreshDatasource();
      "状态更新成功".toast;
    } catch (e) {
      e.toString().toast;
    }
  }

  // 搜索方法
  void searchBenefits() {
    refreshDatasource();
  }

  // 重置搜索条件
  void resetSearch() {
    nameFilter.value = '';
    benefitGroupCodeFilter.value = '';
    statusFilter.value = -1;
    refreshDatasource();
  }
}
