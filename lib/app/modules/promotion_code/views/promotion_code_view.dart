import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/views/exchange_code_add_widget.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/views/promotion_code_add_widget.dart';

import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';

import '../controllers/promotion_code_controller.dart';

class PromotionCodeView extends BasePageListView<PromotionCodeResp, PromotionCodeController> {
  const PromotionCodeView({super.key});

  @override
  List<DataCell> buildDataCells(PromotionCodeResp item, int index) {
    return [
      DataCell(Text(item.id.toString())),
      DataCell(Text(item.code ?? "--")),
      DataCell(Text(item.days.toString())),
      DataCell(Text(item.status == 0 ? "可用" : "不可用")),
      DataCell(Text(item.redeemedTimestamp.toString())),
      DataCell(
        FilledButton(
          onPressed: () {
            Get.dialog(ExchangeCodeAddWidget(resp: item), barrierDismissible: false);
          },
          child: const Text("兑换"),
        ),
      ),
    ];
  }

  @override
  List<DataColumn> getDataColumns() {
    return [
      const DataColumn2(label: Text("ID")),
      const DataColumn2(label: Text("优惠码")),
      const DataColumn2(label: Text("天数")),
      const DataColumn2(label: Text("是否可用")),
      const DataColumn2(label: Text("被使用时间")),
      const DataColumn2(label: Text("兑换")),
    ];
  }

  @override
  String? getModelId(PromotionCodeResp item, int index) {
    return item.id.toString();
  }

  @override
  String getTitle() {
    return "兑换码";
  }

  @override
  void onAddPressed() async {
    await Get.dialog(const PromotionCodeAddWidget(), barrierDismissible: false);
  }
}
