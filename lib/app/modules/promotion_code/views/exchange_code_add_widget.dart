import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/toast.dart';

class ExchangeCodeAddWidget extends StatefulWidget {
  final PromotionCodeResp resp;
  const ExchangeCodeAddWidget({super.key, required this.resp});

  @override
  State<ExchangeCodeAddWidget> createState() => _ExchangeCodeAddWidgetState();
}

class _ExchangeCodeAddWidgetState extends State<ExchangeCodeAddWidget> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Card(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "兑换\n天数:${widget.resp.days}\n兑换码:${widget.resp.code}",
                  style: Get.textTheme.titleLarge,
                ),
                const Gap(20),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      labelText: '用户id',
                      filled: true,
                    ),
                  ),
                ),
                const Gap(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FilledButton(
                      onPressed: () {
                        Net.getRestClient().exchangeCode({"uid": _controller.text, "code": widget.resp.code}).then((value) {
                          Get.back();
                        }).catchError((e) {
                          e.toString().toast;
                        });
                      },
                      child: const Text("兑换"),
                    ),
                    const Gap(10),
                    FilledButton(
                      onPressed: () {
                        Get.back();
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                        foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                      ),
                      child: const Text("取消"),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
