import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/controllers/promotion_code_controller.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';

class PromotionCodeAddWidget extends StatefulWidget {
  final PromotionCodeResp? resp;
  const PromotionCodeAddWidget({super.key, this.resp});

  @override
  State<PromotionCodeAddWidget> createState() => _PromotionCodeAddWidgetState();
}

class _PromotionCodeAddWidgetState extends State<PromotionCodeAddWidget> {
  final TextEditingController _controllerDay = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controllerDay.text = widget.resp?.days?.toString() ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp?.id == null ? "添加兑换码" : "编辑兑换码"),
      content: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _controllerDay,
                decoration: const InputDecoration(
                  labelText: '兑换天数 *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (_controllerDay.text.isEmpty) {
      Get.snackbar("错误", "兑换天数不能为空");
      return false;
    }
    if (int.tryParse(_controllerDay.text) == null) {
      Get.snackbar("错误", "兑换天数必须是数字");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    try {
      PromotionCodeResp tempResourceResp;
      if (widget.resp == null) {
        tempResourceResp = PromotionCodeResp();
      } else {
        tempResourceResp = widget.resp!;
      }
      tempResourceResp.days = int.parse(_controllerDay.text);

      await Get.find<PromotionCodeController>().addEntity(tempResourceResp.toJson());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    _controllerDay.dispose();
    super.dispose();
  }
}
