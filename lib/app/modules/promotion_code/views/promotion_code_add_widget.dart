import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/controllers/promotion_code_controller.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';

class PromotionCodeAddWidget extends StatefulWidget {
  final PromotionCodeResp? resp;
  const PromotionCodeAddWidget({super.key, this.resp});

  @override
  State<PromotionCodeAddWidget> createState() => _PromotionCodeAddWidgetState();
}

class _PromotionCodeAddWidgetState extends State<PromotionCodeAddWidget> {
  final TextEditingController _controllerDay = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controllerDay.text = widget.resp?.days?.toString() ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Card(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "添加兑换码",
                  style: Get.textTheme.titleLarge,
                ),
                const Gap(20),
                const Gap(20),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 300),
                  child: TextField(
                    controller: _controllerDay,
                    decoration: const InputDecoration(
                      labelText: '兑换天数',
                      filled: true,
                    ),
                    keyboardType: TextInputType.number, // 添加数字键盘
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9]')), // 只允许数字输入
                    ],
                  ),
                ),
                const Gap(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FilledButton(
                      onPressed: () {
                        _add();
                        Get.back();
                      },
                      child: Text(widget.resp?.id == null ? "添加" : "更新"),
                    ),
                    const Gap(10),
                    FilledButton(
                      onPressed: () {
                        Get.back();
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                        foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                      ),
                      child: const Text("取消"),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _add() {
    PromotionCodeResp tempResourceResp;
    if (widget.resp == null) {
      tempResourceResp = PromotionCodeResp();
    } else {
      tempResourceResp = widget.resp!;
    }
    tempResourceResp.days = _controllerDay.text.isEmpty ? 0 : int.parse(_controllerDay.text);
    Get.find<PromotionCodeController>().addEntity(tempResourceResp.toJson());
  }
}
