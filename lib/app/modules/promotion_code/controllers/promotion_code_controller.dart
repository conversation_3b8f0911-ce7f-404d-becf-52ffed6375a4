import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/dialog/delete_dialog.dart';
import 'package:lsenglish_admin/dialog/dialog_extension.dart';

import '../views/promotion_code_add_widget.dart';

class PromotionCodeController extends PageListController<PromotionCodeResp> {

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    await Net.getRestClient().addPromotionCode(data);
    refreshDatasource();
  }

  @override
  void deleteByIndex(PromotionCodeResp item, int index) {
    DeleteDialog(
      title: "是否确定删除该优惠码？",
      onPressed: () async {
        try {
          await Net.getRestClient().deletePromotionCode(item.id.toString());
          refreshDatasource();
        } catch (e) {
          e.toString().toast;
        }
      },
    ).dialog();
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().deletePromotionCodeMulti(ids).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  @override
  Future<PageListResp<List<PromotionCodeResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().getPromotionCodes(page, size);
    return result.data;
  }

  @override
  void updateSelect(PromotionCodeResp item, int index) async {
    await Get.dialog(PromotionCodeAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }
}
