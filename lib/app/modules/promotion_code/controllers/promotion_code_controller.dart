import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/toast.dart';

import '../views/promotion_code_add_widget.dart';

class PromotionCodeController extends PageListController<PromotionCodeResp> {
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    await Net.getRestClient().addPromotionCode(data).then((value) => refreshDatasource());
  }

  @override
  void deleteByIndex(PromotionCodeResp item, int index) {
    Net.getRestClient().deletePromotionCode(item.id.toString()).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().deletePromotionCodeMulti(ids).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  @override
  Future<PageListResp<List<PromotionCodeResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().getPromotionCodes(page, size);
    return result.data;
  }

  @override
  void updateSelect(PromotionCodeResp item, int index) async {
    await Get.dialog(PromotionCodeAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }
}
