import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/series_resp/series_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/extension.dart';
import 'package:lsenglish_admin/util/toast.dart';
import 'package:lsenglish_admin/widgets/base_dialog.dart';

import '../views/series_add_widget.dart';

class SeriesController extends PageListController<SeriesResp> {
  @override
  Future<PageListResp<List<SeriesResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    var result = await Net.getRestClient().seriesList(page, size, sortType: sortSelect.value);
    return result.data;
  }

  @override
  void updateSelect(SeriesResp item, int index) async {
    await Get.dialog(SeriesAddWidget(resp: item), barrierDismissible: false);
    refreshDatasource();
  }

  @override
  void deleteByIndex(SeriesResp item, int index) async {
    await Net.getRestClient().deleteSeries(item.id ?? "");
    refreshDatasource();
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    Net.getRestClient().addSeries(data).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  @override
  void deleteMulti(List<String> ids) {
    Net.getRestClient().deleteSeriesMulti(ids).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  @override
  String? getDeleteDialogTitle() {
    return "删除剧集后会删除和剧集关联的信息(不会删除视频)\n是否确定删除?";
  }

  void setFeaturedContent(SeriesResp item) {
    Net.getRestClient().setSeriesFeaturedContent({'id': item.id}).then((value) => refreshDatasource()).catchError((value) => value.toString().toast);
  }

  void setPriority(SeriesResp item) {
    CommonInputDialog(
      title: "修改优先级(越大优先级越高)",
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      inputContent: item.priority.toString(),
      sureCallback: (String content) async {
        if (content.isNotEmpty) {
          await Net.getRestClient()
              .setSeriesPriority({'id': item.id, 'priority': int.parse(content)})
              .then((value) => refreshDatasource())
              .catchError((value) => value.toString().toast);
        }
      },
    ).showDialog;
  }
}
