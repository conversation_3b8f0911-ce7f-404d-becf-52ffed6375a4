import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/series/controllers/series_controller.dart';
import 'package:lsenglish_admin/app/modules/series/views/series_add_widget.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/common/page_view.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';
import 'package:lsenglish_admin/models/series_resp/series_resp.dart';

class SeriesView extends BasePageListView<SeriesResp, SeriesController> {
  const SeriesView({Key? key}) : super(key: key);

  @override
  List<DataColumn> getDataColumns() {
    return const [
      DataColumn2(label: Text("ID")),
      DataColumn2(label: Text("优先级"), fixedWidth: 100),
      DataColumn2(label: Text("标题")),
      DataColumn2(label: Text("描述")),
      DataColumn2(label: Text("声明")),
      DataColumn2(label: Text("是否精选")),
      DataColumn2(label: Text("操作"), fixedWidth: 100),
    ];
  }

  @override
  void onAddPressed() async {
    await Get.dialog(const SeriesAddWidget(), barrierDismissible: false);
  }

  @override
  String getTitle() {
    return "剧集管理";
  }

  @override
  List<DataCell> buildDataCells(SeriesResp item, int index) {
    String title = '--';
    String description = '--';
    String statement = '--';
    if (item.seriesRelations != null && item.seriesRelations!.isNotEmpty) {
      var zh = item.seriesRelations!.firstWhere(
        (e) => e.langCode == 'zh-CN',
        orElse: () => item.seriesRelations!.first,
      );
      title = zh.title ?? '--';
      description = zh.description ?? '--';
      statement = zh.statement ?? '--';
    }
    return [
      DataCell(Text(item.id.toString())),
      DataCell(FilledButton(
        onPressed: () {
          controller.setPriority(item);
        },
        child: Text(item.priority.toString()),
      )),
      DataCell(Text(title)),
      DataCell(Text(description)),
      DataCell(Text(statement)),
      DataCell(
        FilledButton(
          onPressed: () {
            controller.setFeaturedContent(item);
          },
          style: item.isFeaturedContent == true
              ? ButtonStyle(
                  backgroundColor: WidgetStateProperty.all<Color>(Colors.red[700]!),
                  foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                )
              : null,
          child: Text(item.isFeaturedContent == true ? "取消精选" : "设置精选"),
        ),
      ),
      DataCell(
        MenuAnchor(
          builder: (context, controller, child) {
            return IconButton(
              onPressed: () {
                if (controller.isOpen) {
                  controller.close();
                } else {
                  controller.open();
                }
              },
              icon: const Icon(Icons.more_vert),
            );
          },
          menuChildren: [
            MenuItemButton(
              leadingIcon: const Icon(Icons.mode_edit_outlined),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_modify.tr)),
              onPressed: () {
                controller.updateSelect(item, index);
              },
            ),
            MenuItemButton(
              leadingIcon: const Icon(Icons.delete_outline),
              child: Padding(padding: const EdgeInsets.only(right: defaultPadding * 2), child: Text(LocaleKeys.operation_delete.tr)),
              onPressed: () {
                controller.deleteByIndex(item, index);
              },
            )
          ],
        ),
      ),
    ];
  }

  @override
  String? getModelId(SeriesResp item, int index) {
    return item.id;
  }
}
