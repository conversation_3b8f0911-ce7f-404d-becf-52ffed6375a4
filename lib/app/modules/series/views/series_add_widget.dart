import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/series/controllers/series_controller.dart';
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/series_resp/series_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/image.dart';
import 'package:lsenglish_admin/util/oss.dart';

import 'package:lsenglish_admin/models/mother_tongue_resp/mother_tongue_resp.dart';

class SeriesAddWidget extends StatefulWidget {
  final SeriesResp? resp;
  const SeriesAddWidget({super.key, this.resp});

  @override
  State<SeriesAddWidget> createState() => _SeriesAddWidgetState();
}

class _SeriesAddWidgetState extends State<SeriesAddWidget> with TickerProviderStateMixin {
  final TextEditingController _controllerImage = TextEditingController();
  List<String> selectCategoryIds = [];
  List<List<CategoryResp>> categories = [];
  List<String> categoryTypes = ["等级", "主题", "场景"];
  PlatformFile? selectImageFile;
  String imageRemoteUrl = "";

  List<NativeLangResp> langList = [];
  final Map<String, TextEditingController> _titleControllers = {};
  final Map<String, TextEditingController> _descControllers = {};
  final Map<String, TextEditingController> _statementControllers = {};

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    Net.getRestClient().nativeLangList().then((onValue) {
      setState(() {
        langList.clear();
        langList.addAll(onValue.data);

        // 初始化TabController
        _tabController = TabController(length: langList.length, vsync: this);

        // 初始化多语言输入框
        for (var lang in langList) {
          _titleControllers[lang.code ?? ''] = TextEditingController();
          _descControllers[lang.code ?? ''] = TextEditingController();
          _statementControllers[lang.code ?? ''] = TextEditingController();
        }
        // 回填编辑数据
        if (widget.resp?.seriesRelations != null) {
          for (var rel in widget.resp!.seriesRelations!) {
            _titleControllers[rel.langCode ?? '']?.text = rel.title ?? '';
            _descControllers[rel.langCode ?? '']?.text = rel.description ?? '';
            _statementControllers[rel.langCode ?? '']?.text = rel.statement ?? '';
          }
        }
      });
    });
    imageRemoteUrl = widget.resp?.cover ?? "";
    _controllerImage.text = imageRemoteUrl;
    if (widget.resp != null) {
      Net.getRestClient().getSeriesDetail(widget.resp!.id ?? "").then((onValue) {
        setState(() {
          selectCategoryIds = onValue.data.categoryIds ?? [];
        });
      });
    }
    Net.getRestClient().categoryList(1, 100).then((onValue) {
      categories.clear();
      setState(() {
        var levelCategories = onValue.data.data.where((category) => category.categoryTypeId == "1").toList();
        var topicCategories = onValue.data.data.where((category) => category.categoryTypeId == "2").toList();
        var senceCategories = onValue.data.data.where((category) => category.categoryTypeId == "3").toList();
        categories.add(levelCategories);
        categories.add(topicCategories);
        categories.add(senceCategories);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (langList.isEmpty) {
      return const AlertDialog(
        content: Center(child: CircularProgressIndicator()),
      );
    }

    return AlertDialog(
      title: Text(widget.resp?.id == null ? "添加剧集信息" : "编辑剧集信息"),
      content: SizedBox(
        width: 800,
        height: 600,
        child: Column(
          children: [
            // 分类选择区域
            SizedBox(
              height: 200,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("选择分类", style: Get.textTheme.titleMedium),
                    const Gap(8),
                    Wrap(
                      direction: Axis.vertical,
                      children: categories.asMap().entries.map((entry) {
                        int index = entry.key;
                        List<CategoryResp> categoriesItem = entry.value;
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(8),
                            Text(categoryTypes[index], style: Get.textTheme.titleSmall),
                            const Gap(4),
                            Wrap(
                              spacing: 8.0,
                              runSpacing: 4.0,
                              children: categoriesItem
                                  .map((category) => GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            if (selectCategoryIds.contains(category.id)) {
                                              selectCategoryIds.remove(category.id ?? "");
                                            } else {
                                              selectCategoryIds.add(category.id ?? "");
                                            }
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                          decoration: BoxDecoration(
                                              color: selectCategoryIds.contains(category.id) ? Get.theme.primaryColor : Colors.grey,
                                              borderRadius: BorderRadius.circular(12)),
                                          child: Text(
                                            category.name ?? "",
                                            style: const TextStyle(color: Colors.white, fontSize: 12),
                                          ),
                                        ),
                                      ))
                                  .toList(),
                            )
                          ],
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
            const Gap(16),
            // 多语言Tab区域
            Expanded(
              child: Column(
                children: [
                  // Tab标签栏，显示语言状态
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      isScrollable: true,
                      labelColor: Get.theme.primaryColor,
                      unselectedLabelColor: Colors.grey,
                      indicatorSize: TabBarIndicatorSize.tab,
                      tabs: langList.map((lang) {
                        // 检查该语言是否已填写完整
                        final title = _titleControllers[lang.code ?? '']?.text ?? '';
                        final desc = _descControllers[lang.code ?? '']?.text ?? '';
                        final statement = _statementControllers[lang.code ?? '']?.text ?? '';
                        final isComplete = title.isNotEmpty && desc.isNotEmpty && statement.isNotEmpty;
                        final hasPartial = title.isNotEmpty || desc.isNotEmpty || statement.isNotEmpty;

                        return Tab(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isComplete ? Icons.check_circle : (hasPartial ? Icons.warning : Icons.circle_outlined),
                                size: 16,
                                color: isComplete ? Colors.green : (hasPartial ? Colors.orange : Colors.grey),
                              ),
                              const Gap(4),
                              Text('${lang.name}'),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  const Gap(8),
                  // Tab内容区域
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: langList.map((lang) {
                        return SingleChildScrollView(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextField(
                                controller: _titleControllers[lang.code ?? ''],
                                decoration: InputDecoration(
                                  labelText: '${lang.name} 标题 *',
                                  border: const OutlineInputBorder(),
                                ),
                              ),
                              const Gap(16),
                              TextField(
                                controller: _descControllers[lang.code ?? ''],
                                decoration: InputDecoration(
                                  labelText: '${lang.name} 描述 *',
                                  border: const OutlineInputBorder(),
                                ),
                                maxLines: 3,
                              ),
                              const Gap(16),
                              TextField(
                                controller: _statementControllers[lang.code ?? ''],
                                decoration: InputDecoration(
                                  labelText: '${lang.name} 声明 *',
                                  border: const OutlineInputBorder(),
                                ),
                                maxLines: 2,
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
            // 封面上传区域
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controllerImage,
                    onChanged: (value) {
                      setState(() {
                        imageRemoteUrl = value;
                        selectImageFile = null;
                      });
                    },
                    decoration: const InputDecoration(
                      labelText: '封面图片地址',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const Gap(8),
                FilledButton(
                  onPressed: () async {
                    FilePickerResult? result = await FilePicker.platform.pickFiles();
                    if (result != null) {
                      OssUtil().deleteOssFile(imageRemoteUrl);
                      setState(() {
                        selectImageFile = result.files.first;
                        imageRemoteUrl = "";
                        _controllerImage.text = "";
                      });
                    }
                  },
                  child: const Text("选择文件"),
                ),
              ],
            ),
            if (selectImageFile != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text("选择的文件: ${selectImageFile?.name}"),
              ),
            if (imageRemoteUrl.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: ImageLoader(imageRemoteUrl, size: 100),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: Text(widget.resp?.id == null ? "添加" : "更新"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (selectCategoryIds.isEmpty) {
      Get.snackbar("错误", "请选择至少一个资源分类");
      return false;
    }

    // 多语言校验：只要有一个字段有数据，则该语言所有字段都必须填写
    bool hasValidLang = false;
    for (var lang in langList) {
      final title = _titleControllers[lang.code ?? '']?.text ?? '';
      final desc = _descControllers[lang.code ?? '']?.text ?? '';
      final statement = _statementControllers[lang.code ?? '']?.text ?? '';
      final anyFilled = title.isNotEmpty || desc.isNotEmpty || statement.isNotEmpty;
      final allFilled = title.isNotEmpty && desc.isNotEmpty && statement.isNotEmpty;

      if (anyFilled && !allFilled) {
        Get.snackbar("错误", "${lang.name}（${lang.code}）的标题、描述、声明都不能为空");
        return false;
      }
      if (allFilled) {
        hasValidLang = true;
      }
    }

    if (!hasValidLang) {
      Get.snackbar("错误", "请至少填写一个完整的多语言信息（标题、描述、声明）");
      return false;
    }

    if (imageRemoteUrl.isEmpty && selectImageFile == null) {
      Get.snackbar("错误", "请上传封面");
      return false;
    }

    return true;
  }

  Future<void> _submitForm() async {
    try {
      if (selectImageFile != null && imageRemoteUrl.isEmpty) {
        imageRemoteUrl = await OssUtil().uploadSystemResourceImageFile(
          fileBytes: selectImageFile!.bytes,
          fileName: selectImageFile!.name
        ) ?? "";
      }

      SeriesResp tempResourceResp;
      if (widget.resp == null) {
        tempResourceResp = SeriesResp();
      } else {
        tempResourceResp = widget.resp!;
      }

      tempResourceResp.seriesRelations = langList
          .map((lang) => SeriesRelation(
                langCode: lang.code,
                title: _titleControllers[lang.code ?? '']?.text,
                description: _descControllers[lang.code ?? '']?.text,
                statement: _statementControllers[lang.code ?? '']?.text,
              ))
          .where((rel) =>
              (rel.title?.isNotEmpty ?? false) &&
              (rel.description?.isNotEmpty ?? false) &&
              (rel.statement?.isNotEmpty ?? false))
          .toList();

      tempResourceResp.categoryIds = selectCategoryIds;
      tempResourceResp.cover = imageRemoteUrl;

      await Get.find<SeriesController>().addEntity(tempResourceResp.toJson());
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    for (var controller in _titleControllers.values) {
      controller.dispose();
    }
    for (var controller in _descControllers.values) {
      controller.dispose();
    }
    for (var controller in _statementControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
}
