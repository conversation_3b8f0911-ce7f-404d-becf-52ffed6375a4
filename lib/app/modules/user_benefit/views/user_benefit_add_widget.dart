import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';
import '../controllers/user_benefit_controller.dart';

class UserBenefitAddWidget extends StatefulWidget {
  final UserBenefitResp? resp;
  const UserBenefitAddWidget({super.key, this.resp});

  @override
  State<UserBenefitAddWidget> createState() => _UserBenefitAddWidgetState();
}

class _UserBenefitAddWidgetState extends State<UserBenefitAddWidget> {
  final TextEditingController uidController = TextEditingController();
  final TextEditingController benefitGroupCodeController = TextEditingController();
  final TextEditingController benefitIdController = TextEditingController();
  final TextEditingController benefitNameController = TextEditingController();
  final TextEditingController benefitCodeController = TextEditingController();
  final TextEditingController benefitGroupIdController = TextEditingController();
  final TextEditingController remainController = TextEditingController();
  final TextEditingController totalController = TextEditingController();
  final TextEditingController sourceController = TextEditingController();
  
  int selectedStatus = 1; // 默认为启用

  @override
  void initState() {
    super.initState();
    if (widget.resp != null) {
      uidController.text = widget.resp!.uid ?? "";
      benefitGroupCodeController.text = widget.resp!.benefitGroupCode ?? "";
      benefitIdController.text = widget.resp!.benefitId?.toString() ?? "";
      benefitNameController.text = widget.resp!.benefitName ?? "";
      benefitCodeController.text = widget.resp!.benefitCode ?? "";
      benefitGroupIdController.text = widget.resp!.benefitGroupId?.toString() ?? "";
      remainController.text = widget.resp!.remain?.toString() ?? "";
      totalController.text = widget.resp!.total?.toString() ?? "";
      sourceController.text = widget.resp!.source ?? "";
      selectedStatus = widget.resp!.status ?? 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.resp == null ? "添加用户权益" : "编辑用户权益"),
      content: SizedBox(
        width: 700,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: uidController,
                      decoration: const InputDecoration(
                        labelText: "用户ID *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: benefitGroupCodeController,
                      decoration: const InputDecoration(
                        labelText: "权益组编码 *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: benefitIdController,
                      decoration: const InputDecoration(
                        labelText: "权益ID *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: benefitNameController,
                      decoration: const InputDecoration(
                        labelText: "权益名称 *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: benefitCodeController,
                      decoration: const InputDecoration(
                        labelText: "权益编码 *",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: benefitGroupIdController,
                      decoration: const InputDecoration(
                        labelText: "权益组ID",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: remainController,
                      decoration: const InputDecoration(
                        labelText: "剩余数量 *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: TextField(
                      controller: totalController,
                      decoration: const InputDecoration(
                        labelText: "总数量 *",
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                ],
              ),
              const Gap(16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: sourceController,
                      decoration: const InputDecoration(
                        labelText: "来源",
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const Gap(16),
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: "状态 *",
                        border: OutlineInputBorder(),
                      ),
                      value: selectedStatus,
                      items: const [
                        DropdownMenuItem(value: 0, child: Text("失效")),
                        DropdownMenuItem(value: 1, child: Text("启用")),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedStatus = value ?? 1;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text("取消"),
        ),
        FilledButton(
          onPressed: () async {
            if (_validateForm()) {
              await _submitForm();
            }
          },
          child: const Text("确定"),
        ),
      ],
    );
  }

  bool _validateForm() {
    if (uidController.text.isEmpty) {
      Get.snackbar("错误", "用户ID不能为空");
      return false;
    }
    if (benefitGroupCodeController.text.isEmpty) {
      Get.snackbar("错误", "权益组编码不能为空");
      return false;
    }
    if (benefitIdController.text.isEmpty) {
      Get.snackbar("错误", "权益ID不能为空");
      return false;
    }
    if (benefitNameController.text.isEmpty) {
      Get.snackbar("错误", "权益名称不能为空");
      return false;
    }
    if (benefitCodeController.text.isEmpty) {
      Get.snackbar("错误", "权益编码不能为空");
      return false;
    }
    if (remainController.text.isEmpty) {
      Get.snackbar("错误", "剩余数量不能为空");
      return false;
    }
    if (totalController.text.isEmpty) {
      Get.snackbar("错误", "总数量不能为空");
      return false;
    }
    return true;
  }

  Future<void> _submitForm() async {
    Map<String, dynamic> data = {
      'uid': uidController.text,
      'benefitGroupCode': benefitGroupCodeController.text,
      'benefitId': int.parse(benefitIdController.text),
      'benefitName': benefitNameController.text,
      'benefitCode': benefitCodeController.text,
      'remain': int.parse(remainController.text),
      'total': int.parse(totalController.text),
      'status': selectedStatus,
      'source': sourceController.text,
    };

    if (benefitGroupIdController.text.isNotEmpty) {
      data['benefitGroupId'] = int.parse(benefitGroupIdController.text);
    }

    if (widget.resp != null) {
      data['id'] = widget.resp!.id;
    }

    try {
      await Get.find<UserBenefitController>().addEntity(data);
      Get.back();
      Get.snackbar("成功", widget.resp == null ? "添加成功" : "编辑成功");
    } catch (e) {
      Get.snackbar("错误", e.toString());
    }
  }

  @override
  void dispose() {
    uidController.dispose();
    benefitGroupCodeController.dispose();
    benefitIdController.dispose();
    benefitNameController.dispose();
    benefitCodeController.dispose();
    benefitGroupIdController.dispose();
    remainController.dispose();
    totalController.dispose();
    sourceController.dispose();
    super.dispose();
  }
}
