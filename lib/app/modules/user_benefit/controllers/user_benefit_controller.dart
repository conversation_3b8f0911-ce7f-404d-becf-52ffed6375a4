import 'package:get/get.dart';
import 'package:lsenglish_admin/common/page_list_controller.dart';
import 'package:lsenglish_admin/models/benefit_resp/benefit_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/net/net.dart';
import 'package:lsenglish_admin/util/toast.dart';

class UserBenefitController extends PageListController<UserBenefitResp> {
  // 搜索条件
  var uidFilter = ''.obs;
  var benefitGroupCodeFilter = ''.obs;
  var benefitCodeFilter = ''.obs;
  var statusFilter = (-1).obs; // -1表示全部，0失效，1启用

  @override
  Future<PageListResp<List<UserBenefitResp>>> getPageList(int startIndex, int size) async {
    int page = startIndex ~/ size + 1;
    
    // 构建查询参数
    String? uid = uidFilter.value.isEmpty ? null : uidFilter.value;
    String? benefitGroupCode = benefitGroupCodeFilter.value.isEmpty ? null : benefitGroupCodeFilter.value;
    String? benefitCode = benefitCodeFilter.value.isEmpty ? null : benefitCodeFilter.value;
    int? status = statusFilter.value == -1 ? null : statusFilter.value;
    
    var result = await Net.getRestClient().userBenefitList(
      page, 
      size,
      uid: uid,
      benefitGroupCode: benefitGroupCode,
      benefitCode: benefitCode,
      status: status,
    );
    return result.data;
  }

  @override
  void updateSelect(UserBenefitResp item, int index) async {
    // 用户权益只能查看，不能修改
    Get.snackbar("提示", "用户权益只能查看，不能修改");
  }

  @override
  void deleteByIndex(UserBenefitResp item, int index) async {
    // 用户权益只能查看，不能删除
    Get.snackbar("提示", "用户权益只能查看，不能删除");
  }

  @override
  Future<void> addEntity(Map<String, dynamic> data) async {
    // 用户权益只能查看，不能添加或修改
    Get.snackbar("提示", "用户权益只能查看，不能添加或修改");
  }

  @override
  void deleteMulti(List<String> ids) {
    // 用户权益只能查看，不能批量删除
    Get.snackbar("提示", "用户权益只能查看，不能删除");
  }

  // 刷新用户权益
  void refreshUserBenefit(UserBenefitResp item) async {
    Map<String, dynamic> data = {
      'id': item.id,
    };
    
    try {
      await Net.getRestClient().refreshUserBenefit(data);
      refreshDatasource();
      "权益刷新成功".toast;
    } catch (e) {
      e.toString().toast;
    }
  }

  // 切换状态
  void toggleStatus(UserBenefitResp item) async {
    int newStatus = item.status == 1 ? 0 : 1;
    Map<String, dynamic> data = {
      'id': item.id,
      'status': newStatus,
    };
    
    try {
      await Net.getRestClient().updateUserBenefit(data);
      refreshDatasource();
      "状态更新成功".toast;
    } catch (e) {
      e.toString().toast;
    }
  }

  // 批量分配权益给用户
  void assignBenefitsToUser(String uid, List<int> benefitIds) async {
    Map<String, dynamic> data = {
      'uid': uid,
      'benefitIds': benefitIds,
    };
    
    try {
      await Net.getRestClient().assignBenefitsToUser(data);
      refreshDatasource();
      "权益分配成功".toast;
    } catch (e) {
      e.toString().toast;
    }
  }

  // 搜索方法
  void searchUserBenefits() {
    refreshDatasource();
  }

  // 重置搜索条件
  void resetSearch() {
    uidFilter.value = '';
    benefitGroupCodeFilter.value = '';
    benefitCodeFilter.value = '';
    statusFilter.value = -1;
    refreshDatasource();
  }
}
