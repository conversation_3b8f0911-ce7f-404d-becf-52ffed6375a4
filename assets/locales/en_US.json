{"drawer": {"home": "Dashboards", "user_manager": "User Manager", "role_manager": "Role Manager", "permission_manager": "Permission Manager", "user_title": "User", "permission": "Permission", "permission_page": "Page Permission", "permission_role": "Role Permission", "system_setting": "System Setting", "scheduled_tasks": "Scheduled Task", "theme_setting": "Theme Setting", "locales_setting": "Multi-Language"}, "common": {"login": "<PERSON><PERSON>", "logout": "Logout"}, "tooltip": {"toggle_brightness": "Toggle brightness", "select_color_seed": "Select a seed color", "select_color_images": "Select a color extraction image"}, "user": {"name": "UserName", "role": "UserRole", "create_time": "CreateTime", "status": "Status"}, "operation": {"modify": "Modify", "delete": "Delete"}, "net": {"connectionTimeout": "Connection Timeout", "sendTimeout": "Server exception, please try again later!", "receiveTimeout": "网络连接超时，请检查网络设置", "badResponse": "Network connection timed out, please check network settings", "cancel": "The request has been canceled, please request again", "connectionError": "Network abnormality, please try again later!", "400": "Request syntax error", "401": "Unauthorized, please log in", "403": "Access Denied", "404": "Request address 404", "408": "Request timed out", "500": "Server exception", "501": "Service not implemented", "502": "Gateway error", "503": "Service is not available", "504": "Gateway timeout", "505": "HTTP version is not supported", "default_code_error": "Request failed, error code"}, "input": {"user_name_label": "UserName", "user_name_error": "Please input username", "password_label": "Password", "password_error": "Please input password"}}