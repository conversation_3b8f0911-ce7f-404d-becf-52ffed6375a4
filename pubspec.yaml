name: lsenglish_admin
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.1.4 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  get: ^4.6.6
  flutter_svg: ^2.0.9
  fl_chart: ^0.64.0
  dio: ^5.3.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1
  connectivity_plus: ^5.0.1
  dio_cache_interceptor: ^3.4.4
  data_table_2: ^2.6.0
  retrofit: ^4.0.3
  gap: ^3.0.1
  delightful_toast: ^1.1.0
  file_picker: ^8.3.1
  extended_image: ^8.2.0
  path_provider: ^2.1.3
  intl: ^0.19.0
  shelf: ^1.4.1
  shelf_proxy: ^1.0.4
  flutter_easyloading: ^3.0.5
  video_player: ^2.9.2
  html: ^0.15.4
  webview_flutter: ^4.9.0
  webview_flutter_android: ^3.16.7
  webview_flutter_wkwebview: ^3.15.0
  crypto: ^3.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  retrofit_generator: ">=7.0.0 <8.0.0"
  flutter_lints: ^2.0.0
  build_runner: ^2.4.6
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
